import{ao as E,ap as S,q as w,r as n,j as s,v as d,C as I,x as a,P as g,U as x,B as j,y as R,N as m,O as r,$ as B,a0 as D,aq as N,ag as P,aa as k}from"./index-ClZegq-Q.js";const z=()=>{const y=E(),i=S();w();const[f,u]=n.useState(!0),[h,p]=n.useState(null),[e,v]=n.useState(null),[C,b]=n.useState(null),{patientId:o,responseId:c}=y.state||{};return n.useEffect(()=>{(async()=>{if(!o||!c){p("No se encontró información del paciente o la respuesta"),u(!1);return}try{const t=await P.getById(o);if(!t.success)throw new Error(t.message||"Error al cargar datos del paciente");v(t.data);const l=await k.getById(c);if(!l.success)throw new Error(l.message||"Error al cargar datos de la respuesta");b(l.data)}catch(t){console.error("Error loading results data:",t),p(t.message||"Error al cargar las respuestas")}finally{u(!1)}})()},[o,c]),f?s.jsxs(d,{maxWidth:"lg",sx:{py:4,textAlign:"center"},children:[s.jsx(I,{size:60}),s.jsx(a,{variant:"h6",sx:{mt:2},children:"Cargando respuestas..."})]}):h||!e||!C?s.jsx(d,{maxWidth:"lg",sx:{py:4},children:s.jsxs(g,{sx:{p:4,textAlign:"center"},children:[s.jsx(a,{variant:"h5",color:"error",gutterBottom:!0,children:h||"No se pudieron cargar las respuestas"}),s.jsx(x,{variant:"contained",onClick:()=>i("/respuestas-cuestionario"),sx:{mt:2},children:"Ver todas las respuestas"})]})}):s.jsx(d,{maxWidth:"lg",sx:{py:4},children:s.jsxs(g,{sx:{p:4,mb:4},children:[s.jsxs(j,{sx:{display:"flex",alignItems:"center",mb:3},children:[s.jsx(R,{sx:{color:"success.main",fontSize:40,mr:2}}),s.jsx(a,{variant:"h4",component:"h1",children:"Respuestas del Cuestionario"})]}),s.jsx(m,{sx:{mb:3}}),s.jsx(r,{container:!0,spacing:3,children:s.jsx(r,{item:!0,xs:12,md:6,children:s.jsx(B,{sx:{borderRadius:"16px",boxShadow:"0 8px 16px rgba(0,0,0,0.1)",p:2},children:s.jsxs(D,{children:[s.jsxs(r,{container:!0,spacing:2,alignItems:"center",children:[s.jsx(r,{item:!0,children:s.jsx(N,{sx:{bgcolor:e.gender==="M"?"primary.main":"secondary.main",width:56,height:56,fontSize:"1.5rem"},children:e.name.charAt(0)})}),s.jsxs(r,{item:!0,xs:!0,children:[s.jsx(a,{variant:"h6",sx:{fontWeight:"bold"},children:e.name}),s.jsx(a,{variant:"body2",color:"text.secondary",children:e.school_grade||"Grado no especificado"})]})]}),s.jsx(m,{sx:{my:2}}),s.jsxs(r,{container:!0,spacing:1,children:[s.jsx(r,{item:!0,xs:6,children:s.jsxs(a,{variant:"body1",children:[s.jsx("strong",{children:"Edad:"})," ",e.age," años"]})}),s.jsx(r,{item:!0,xs:6,children:s.jsxs(a,{variant:"body1",children:[s.jsx("strong",{children:"Sexo:"})," ",e.gender==="M"?"Masculino":"Femenino"]})}),s.jsx(r,{item:!0,xs:12,children:s.jsx(a,{variant:"body1",color:"text.secondary",children:e.psychologist?`Psicólogo: ${e.psychologist}`:"Sin psicólogo asignado"})})]})]})})})}),s.jsxs(j,{sx:{mt:4,display:"flex",justifyContent:"space-between"},children:[s.jsx(x,{variant:"outlined",onClick:()=>i("/respuestas-cuestionario"),children:"Ver todas las respuestas"}),s.jsx(x,{variant:"contained",color:"primary",onClick:()=>i("/cuestionario"),children:"Nuevo cuestionario"})]})]})})};export{z as default};
