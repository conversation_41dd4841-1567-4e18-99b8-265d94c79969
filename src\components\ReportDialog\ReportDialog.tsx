// Componente separado para el diálogo de reportes
// Mejora la modularidad y mantenibilidad del código

import React, { useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  Grid
} from '@mui/material';
import {
  Close as CloseIcon,
  Print as PrintIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Report } from '../../hooks/useReports';
import { useReportUtils } from '../../hooks/useReports';
import PatientInfo from './PatientInfo';
import ScoresTables from './ScoresTables';
import GraphicsSection from './GraphicsSection';
import { NewGraphicsSectionComplete } from './NewGraphicsSection';
import QualitativeReport from './QualitativeReport';
import ImprovedQualitativeReport from './ImprovedQualitativeReport';

interface ReportDialogProps {
  open: boolean;
  report: Report | null;
  onClose: () => void;
}

const ReportDialog: React.FC<ReportDialogProps> = ({ open, report, onClose }) => {
  const { calculateAge, formatDate } = useReportUtils();

  const handleDownloadPDF = useCallback(() => {
    if (!report) return;
    
    const originalTitle = document.title;
    document.title = `Informe_MACI-II_${report.patient_name.replace(/\s+/g, '_')}_${formatDate(report.test_date)}`;

    // Crear estilos específicos para impresión y PDF
    const printStyles = `
      <style>
        @media print, screen {
          body { 
            font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
            font-size: 14px !important;
            line-height: 1.6 !important;
            color: #000 !important;
            background: white !important;
          }
          
          .MuiDialog-paper {
            box-shadow: none !important;
            margin: 0 !important;
            max-width: none !important;
            max-height: none !important;
            background: white !important;
          }
          
          .MuiDialogTitle-root {
            background: #1e3a5f !important;
            color: white !important;
            text-align: center !important;
            padding: 16px !important;
            font-size: 20px !important;
            font-weight: bold !important;
          }
          
          /* Información del paciente */
          .patient-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
            border: 1px solid #e3f2fd !important;
            border-radius: 12px !important;
            padding: 32px !important;
            margin-bottom: 32px !important;
            position: relative !important;
          }
          
          .patient-info::before {
            content: "" !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: 4px !important;
            background: linear-gradient(90deg, #1e3a5f 0%, #048799 100%) !important;
            border-radius: 12px 12px 0 0 !important;
          }
          
          /* Tablas de escalas */
          .MuiTable-root {
            border-collapse: collapse !important;
            width: 100% !important;
            margin-bottom: 24px !important;
          }
          
          .MuiTableCell-root {
            border: 1px solid #ddd !important;
            padding: 12px 8px !important;
            font-size: 14px !important;
          }
          
          .MuiTableCell-head {
            background-color: #1e3a5f !important;
            color: white !important;
            font-weight: bold !important;
            text-align: center !important;
            font-size: 14px !important;
          }
          
          /* Círculos de escalas */
          .scale-circle {
            width: 40px !important;
            height: 40px !important;
            border-radius: 50% !important;
            background-color: #048799 !important;
            color: white !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-weight: bold !important;
            font-size: 14px !important;
            margin: 0 auto !important;
          }
          
          /* Nombres de escalas */
          .scale-name {
            color: #000000 !important;
            font-weight: bold !important;
            font-size: 14px !important;
          }
          
          .scale-subname {
            color: #000000 !important;
            font-weight: normal !important;
            font-size: 13px !important;
          }
          
          /* Barras de progreso */
          .progress-bar {
            height: 20px !important;
            background-color: #f0f0f0 !important;
            position: relative !important;
          }
          
          .progress-fill {
            height: 100% !important;
            transition: none !important;
          }
          
          /* Interpretación cualitativa */
          .qualitative-content {
            background: #ffffff !important;
            border: 1px solid #e0e0e0 !important;
            border-radius: 6px !important;
            padding: 24px !important;
            font-size: 14px !important;
            line-height: 1.7 !important;
            color: #2c3e50 !important;
          }
          
          .section-title {
            background-color: #1e3a5f !important;
            color: white !important;
            padding: 12px 20px !important;
            margin: 24px 0 16px 0 !important;
            border-radius: 6px !important;
            text-align: center !important;
            font-weight: bold !important;
            font-size: 16px !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
          }
          
          @page {
            margin: 1.5cm !important;
            size: A4 !important;
          }
          
          .no-print {
            display: none !important;
          }
        }
      </style>
    `;

    // Agregar estilos al documento
    const styleElement = document.createElement('style');
    styleElement.innerHTML = printStyles;
    document.head.appendChild(styleElement);

    // Capturar el contenido del diálogo
    const dialogContent = document.querySelector('.MuiDialog-paper');
    if (dialogContent) {
      html2canvas(dialogContent as HTMLElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: dialogContent.scrollWidth,
        height: dialogContent.scrollHeight
      }).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF('p', 'mm', 'a4');
        const imgWidth = 210;
        const pageHeight = 295;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;
        let position = 0;

        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        while (heightLeft >= 0) {
          position = heightLeft - imgHeight;
          pdf.addPage();
          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;
        }

        pdf.save(`Informe_MACI-II_${report.patient_name.replace(/\s+/g, '_')}_${formatDate(report.test_date)}.pdf`);
        
        // Restaurar título original y limpiar estilos
        document.title = originalTitle;
        if (styleElement) styleElement.remove();
      }).catch(error => {
        console.error('Error generating PDF:', error);
        // Restaurar título original y limpiar estilos en caso de error
        document.title = originalTitle;
        if (styleElement) styleElement.remove();
      });
    }
  }, [report, formatDate]);

  if (!report) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          maxHeight: '90vh',
          height: 'auto'
        }
      }}
    >
      <DialogTitle
        sx={{
          backgroundColor: '#222C4D',
          color: 'white',
          textAlign: 'center',
          position: 'relative',
          padding: '16px 24px'
        }}
      >
        <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
          Informe MACI-II - {report.patient_name}
        </Typography>
        <IconButton
          className="no-print"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white'
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ padding: '24px', backgroundColor: '#fafafa' }}>
        <Grid container spacing={4}>
          {/* 1. Información del Paciente */}
          <Grid item xs={12}>
            <Typography 
              variant="h4" 
              sx={{ 
                color: '#1e3a5f', 
                fontWeight: 'bold', 
                mb: 3,
                textAlign: 'center',
                fontSize: '1.8rem',
                textTransform: 'uppercase'
              }}
            >
              1. INFORMACIÓN DEL PACIENTE
            </Typography>
            <PatientInfo 
              report={report}
              calculateAge={calculateAge}
              formatDate={formatDate}
            />
          </Grid>

          {/* 2. Análisis Gráfico por Escalas */}
          <Grid item xs={12}>
            <NewGraphicsSectionComplete report={report} />
          </Grid>

          {/* 3. Interpretación Cualitativa */}
          <Grid item xs={12}>
            <Typography 
              variant="h4" 
              sx={{ 
                color: '#1e3a5f', 
                fontWeight: 'bold', 
                mb: 3,
                textAlign: 'center',
                fontSize: '1.8rem',
                textTransform: 'uppercase'
              }}
            >
              3. INTERPRETACIÓN CUALITATIVA
            </Typography>
            <ImprovedQualitativeReport 
              report={report}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions className="no-print" sx={{ padding: '16px 24px', backgroundColor: '#f5f5f5' }}>
        <Button
          onClick={handleDownloadPDF}
          variant="contained"
          startIcon={<DownloadIcon />}
          sx={{
            backgroundColor: '#222C4D',
            '&:hover': {
              backgroundColor: '#1a1f3a'
            }
          }}
        >
          Descargar PDF
        </Button>
        <Button
          onClick={() => window.print()}
          variant="outlined"
          startIcon={<PrintIcon />}
          sx={{
            borderColor: '#222C4D',
            color: '#222C4D',
            '&:hover': {
              borderColor: '#1a1f3a',
              backgroundColor: 'rgba(34, 44, 77, 0.04)'
            }
          }}
        >
          Imprimir
        </Button>
        <Button onClick={onClose} color="inherit">
          Cerrar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default React.memo(ReportDialog);