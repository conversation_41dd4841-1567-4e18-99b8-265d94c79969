// Componente dedicado exclusivamente a mostrar los gráficos
// Separado de las tablas para seguir el diseño solicitado:
// 1. Información general del paciente
// 2. Gráficos
// 3. Interpretación cualitativa

import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper
} from '@mui/material';
import { Report } from '../../types/Report';
import { getScaleName } from '../../config/scaleNames';
import { BarChartGroup } from './BarChart';

interface GraphicsSectionProps {
  report: Report;
}

// Componente para mostrar un grupo de gráficos
const ChartGroup: React.FC<{
  title: string;
  scales: Record<string, { raw_score: number | null; pc_score: number | null }>;
  scaleType: 'validity' | 'personality' | 'concerns' | 'clinical' | 'grossman';
}> = React.memo(({ title, scales, scaleType }) => {
  const scaleData = Object.entries(scales).map(([scaleId, scores]) => ({
    scaleId,
    scaleName: getScaleName(scaleId, scaleType),
    pcScore: scores.pc_score || 0
  }));

  return (
    <Paper 
      elevation={1} 
      sx={{ 
        padding: '20px', 
        marginBottom: '20px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #dee2e6'
      }}
    >
      <Typography 
        variant="h6" 
        sx={{ 
          color: '#222C4D', 
          fontWeight: 'bold', 
          marginBottom: '16px',
          textAlign: 'center',
          fontSize: '1.1rem',
          textTransform: 'uppercase'
        }}
      >
        {title}
      </Typography>
      
      <BarChartGroup scales={scaleData} />
    </Paper>
  );
});

const GraphicsSection: React.FC<GraphicsSectionProps> = ({ report }) => {
  return (
    <Box sx={{ marginBottom: '32px' }}>
      <Typography 
        variant="h4" 
        sx={{ 
          color: '#222C4D', 
          fontWeight: 'bold', 
          marginBottom: '32px',
          textAlign: 'center',
          fontSize: '2rem',
          textTransform: 'uppercase',
          letterSpacing: '1px',
          borderBottom: '3px solid #222C4D',
          paddingBottom: '16px'
        }}
      >
        Perfil Gráfico de Resultados
      </Typography>

      <Grid container spacing={3}>
        {/* Escalas de Validez */}
        {report.validity_scales && Object.keys(report.validity_scales).length > 0 && (
          <Grid item xs={12}>
            <ChartGroup
              title="Escalas de Validez"
              scales={report.validity_scales}
              scaleType="validity"
            />
          </Grid>
        )}

        {/* Patrones de Personalidad */}
        {report.personality_patterns && Object.keys(report.personality_patterns).length > 0 && (
          <Grid item xs={12}>
            <ChartGroup
              title="Patrones de Personalidad"
              scales={report.personality_patterns}
              scaleType="personality"
            />
          </Grid>
        )}

        {/* Preocupaciones Expresadas */}
        {report.expressed_concerns && Object.keys(report.expressed_concerns).length > 0 && (
          <Grid item xs={12}>
            <ChartGroup
              title="Preocupaciones Expresadas"
              scales={report.expressed_concerns}
              scaleType="concerns"
            />
          </Grid>
        )}

        {/* Síndromes Clínicos */}
        {report.clinical_syndromes && Object.keys(report.clinical_syndromes).length > 0 && (
          <Grid item xs={12}>
            <ChartGroup
              title="Síndromes Clínicos"
              scales={report.clinical_syndromes}
              scaleType="clinical"
            />
          </Grid>
        )}

        {/* Facetas de Grossman */}
        {report.grossman_facets && Object.keys(report.grossman_facets).length > 0 && (
          <Grid item xs={12}>
            <ChartGroup
              title="Facetas de Grossman"
              scales={report.grossman_facets}
              scaleType="grossman"
            />
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default React.memo(GraphicsSection);