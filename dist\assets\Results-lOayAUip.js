import{ap as U,at as H,r as C,j as e,v as G,C as J,x as r,P as O,U as Q,N as z,O as E,$ as L,a0 as T,B as s,aa as X,ag as Z,t as V,ah as K,q as ee,aq as se}from"./index-ClZegq-Q.js";const Y=P=>{const i=new Date(P),p=new Date;let a=p.getFullYear()-i.getFullYear();const d=p.getMonth()-i.getMonth();return(d<0||d===0&&p.getDate()<i.getDate())&&a--,a},q=({scaleId:P,scaleName:i,pd:p,pc:a,description:d,isSubscale:b=!1})=>{const x=ee(),_=k=>{const m=Number(k);return isNaN(m)?x.palette.grey[300]:m>=85?"#d32f2f":m>=75?"#f57c00":m>=60?"#fbc02d":m>=35?"#4caf50":"#1976d2"},t=typeof a=="number"?a:0,B=t>100?"100%":`${t}%`,A=_(a);return e.jsxs(s,{sx:{display:"flex",alignItems:"center",mb:1,width:"100%",pl:b?4:0,pr:1,py:.5,backgroundColor:b?"#f0f4f0":"transparent",borderRadius:b?"4px":0},children:[e.jsx(s,{sx:{width:"50px",textAlign:"center",mr:1,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(se,{sx:{bgcolor:_(a),width:32,height:32,fontSize:"0.8rem"},children:P})}),e.jsxs(s,{sx:{flex:1},children:[e.jsx(r,{variant:"subtitle1",sx:{fontWeight:"bold"},children:i}),d&&e.jsx(r,{variant:"caption",color:"text.secondary",children:d})]}),e.jsx(s,{sx:{width:"50px",textAlign:"center"},children:e.jsx(r,{variant:"body1",children:p})}),e.jsx(s,{sx:{width:"50px",textAlign:"center"},children:e.jsx(r,{variant:"body1",children:a})}),e.jsx(s,{sx:{flex:2,ml:2},children:e.jsx(s,{sx:{height:"20px",backgroundColor:x.palette.grey[200],borderRadius:"4px",overflow:"hidden"},children:e.jsx(s,{sx:{height:"100%",width:B,backgroundColor:A,transition:"width 0.5s ease-in-out"}})})})]})},te=()=>{const P=U(),{responseId:i}=H(),[p,a]=C.useState(!0),[d,b]=C.useState(null),[x,_]=C.useState(null),[t,B]=C.useState(null),[A,k]=C.useState({});if(C.useEffect(()=>{(async()=>{if(!i){b("No se proporcionó un ID de respuesta"),a(!1);return}try{a(!0);const n=await X.getById(i);if(!n.success||!n.data)throw new Error(n.message||"Error al cargar datos de la respuesta");B(n.data);const f=await Z.getById(n.data.patient_id);if(f.success&&f.data){_(f.data);const u=Y(f.data.birthDate),o=n.data.scores,c=u>=13&&u<=15?"ages13_15":"ages16_18",R=u>=13&&u<=15?"13_15":"16_18",{data:y,error:F}=await V.from("baremos_main").select("raw_score,category,scale,br_score").eq("age_range",c),{data:S,error:j}=await V.from("baremos_grossman").select("raw_score,scale,percentile").eq("age_range",R);if(F||j)throw new Error("Error al cargar los baremos de Supabase");const v={},W={personalityPatterns:"Personality Patterns",expressedConcerns:"Expressed Concerns",clinicalSyndromes:"Clinical Syndromes",grossmanFacetScales:"Grossman Facet Scales"};for(const l in o){v[l]={};for(const h in o[l]){const D=o[l][h];if(l==="grossmanFacetSubscales"){const g=S==null?void 0:S.find(I=>I.scale===h&&I.raw_score===D);g&&(v[l][h]={pc:g.percentile})}else{const g=W[l];if(g){const I=y==null?void 0:y.find(M=>M.category===g&&M.scale===h&&M.raw_score===D);I&&(v[l][h]={pc:I.br_score})}}}}k(v)}else throw new Error("Error al cargar los datos del paciente")}catch(n){console.error("Error loading results data:",n),b(n.message||"Error al cargar los resultados")}finally{a(!1)}})()},[i]),p)return e.jsxs(G,{maxWidth:"lg",sx:{py:4,textAlign:"center"},children:[e.jsx(J,{size:60}),e.jsx(r,{variant:"h6",sx:{mt:2},children:"Cargando resultados..."})]});if(d||!x||!t)return e.jsx(G,{maxWidth:"lg",sx:{py:4},children:e.jsxs(O,{sx:{p:4,textAlign:"center"},children:[e.jsx(r,{variant:"h5",color:"error",gutterBottom:!0,children:d||"No se pudieron cargar los resultados"}),e.jsx(Q,{variant:"contained",onClick:()=>P("/respuestas-cuestionario"),sx:{mt:2},children:"Ver todas las respuestas"})]})});const m=Y(x.birthDate),$=t!=null&&t.date?new Date(t.date).toLocaleDateString("es-ES"):"N/A",N=(w,n)=>{if(!(t!=null&&t.scores[w]))return null;const f=t.scores[w],u=K[w];return e.jsx(E,{item:!0,xs:12,children:e.jsx(L,{variant:"outlined",children:e.jsxs(T,{children:[e.jsx(r,{variant:"h5",gutterBottom:!0,sx:{fontWeight:"bold",mb:2},children:n}),e.jsx(z,{sx:{mb:2}}),Object.keys(f).map(o=>{var F,S;const c=u==null?void 0:u.scales[o];if(!c)return null;const R=f[o],y=((S=(F=A[w])==null?void 0:F[o])==null?void 0:S.pc)??"-";return w==="grossmanFacetScales"&&c.subscales?e.jsxs(s,{sx:{mb:3},children:[e.jsx(q,{scaleId:o,scaleName:c.name,pd:R,pc:y,description:c.description}),e.jsx(s,{sx:{mt:1},children:Object.keys(c.subscales).map(j=>{var h,D,g;const v=c.subscales[j],W=(h=t.scores.grossmanFacetSubscales)==null?void 0:h[j],l=((g=(D=A.grossmanFacetSubscales)==null?void 0:D[j])==null?void 0:g.pc)??"-";return W===void 0?null:e.jsx(q,{scaleId:j,scaleName:v.name,pd:W,pc:l,isSubscale:!0},j)})})]},o):e.jsx(q,{scaleId:o,scaleName:c.name,pd:R,pc:y,description:c.description},o)})]})})})};return e.jsxs(G,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(O,{sx:{p:3,mb:4,backgroundColor:"background.paper",borderRadius:"8px"},children:[e.jsx(r,{variant:"h4",gutterBottom:!0,sx:{fontWeight:"bold",color:"primary.main"},children:"Informe de Resultados MACI-II"}),e.jsx(z,{sx:{mb:3}}),e.jsxs(E,{container:!0,spacing:2,children:[e.jsxs(E,{item:!0,xs:12,md:6,children:[e.jsxs(r,{variant:"body1",children:[e.jsx("strong",{children:"Nombre:"})," ",x.name]}),e.jsxs(r,{variant:"body1",children:[e.jsx("strong",{children:"Edad:"})," ",m," años"]}),e.jsxs(r,{variant:"body1",children:[e.jsx("strong",{children:"Sexo:"})," ",x.gender==="M"?"Masculino":"Femenino"]})]}),e.jsxs(E,{item:!0,xs:12,md:6,children:[e.jsxs(r,{variant:"body1",children:[e.jsx("strong",{children:"ID del Paciente:"})," ",x.id]}),e.jsxs(r,{variant:"body1",children:[e.jsx("strong",{children:"ID de la Respuesta:"})," ",i]}),e.jsxs(r,{variant:"body1",children:[e.jsx("strong",{children:"Fecha de Evaluación:"})," ",$]})]})]})]}),e.jsxs(E,{container:!0,spacing:4,children:[e.jsx(E,{item:!0,xs:12,children:e.jsx(L,{variant:"outlined",children:e.jsx(T,{sx:{pb:"16px !important"},children:e.jsxs(s,{sx:{display:"flex",alignItems:"center",width:"100%",pr:1,py:.5,color:"text.secondary"},children:[e.jsx(s,{sx:{width:"50px",textAlign:"center",mr:1,fontWeight:"bold"},children:"ID"}),e.jsx(s,{sx:{flex:1,fontWeight:"bold"},children:"Escala"}),e.jsx(s,{sx:{width:"50px",textAlign:"center",fontWeight:"bold"},children:"PD"}),e.jsx(s,{sx:{width:"50px",textAlign:"center",fontWeight:"bold"},children:"PC"}),e.jsx(s,{sx:{flex:2,ml:2,textAlign:"left",fontWeight:"bold"},children:"Perfil de Puntuación Centil"})]})})})}),N("personalityPatterns","Patrones de Personalidad Clínica"),N("expressedConcerns","Preocupaciones Expresadas"),N("clinicalSyndromes","Síndromes Clínicos"),N("grossmanFacetScales","Escalas de Facetas de Grossman")]})]})};export{te as default};
