import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider
} from '@mui/material';
import { Report } from '../../types/Report';

interface ImprovedQualitativeReportProps {
  report: Report;
}

const ImprovedQualitativeReport: React.FC<ImprovedQualitativeReportProps> = ({ report }) => {
  if (!report) {
    return null;
  }

  // Obtener la interpretación cualitativa del reporte
  const qualitativeInterpretation = report.qualitative_interpretation || 'No hay interpretación cualitativa disponible.';

  // Función para limpiar texto de palabras en inglés y mayúsculas no deseadas
  const cleanText = (text: string) => {
    let cleanedText = text;
    
    // Eliminar palabras en inglés comunes en reportes psicológicos
    const englishWords = [
      '\\(Aggrieved\\)',
      '\\(Family Discord\\)',
      '\\(Depressive Affect\\)',
      '\\(Anxiety\\)',
      '\\(Substance Abuse\\)',
      '\\(Delinquent Predisposition\\)',
      '\\(Eating Dysfunctions\\)',
      '\\(Suicidal Tendency\\)',
      '\\(Mood Dysregulation\\)',
      '\\(Posttraumatic Stress\\)',
      '\\(Reality Distortion\\)',
      '\\(Identity Diffusion\\)',
      '\\(Self-Devaluation\\)',
      '\\(Peer Insecurity\\)',
      '\\(Introversive\\)',
      '\\(Inhibited\\)',
      '\\(Submissive\\)',
      '\\(Dramatizing\\)',
      '\\(Egotistic\\)',
      '\\(Unruly\\)',
      '\\(Forceful\\)',
      '\\(Conforming\\)',
      '\\(Oppositional\\)',
      '\\(Self-Demeaning\\)',
      '\\(Borderline Tendency\\)'
    ];
    
    englishWords.forEach(word => {
      const regex = new RegExp(word, 'gi');
      cleanedText = cleanedText.replace(regex, '');
    });
    
    // Eliminar frases en mayúsculas no deseadas
    const unwantedUppercase = [
      'PUNTUACIÓN BAJA',
      'PRESENCIA DE PREOCUPACIONES',
      'CLÍNICAMENTE RELEVANTE',
      'PATRÓN PROMINENTE',
      'SÍNDROME MODERADO',
      'DISFUNCIÓN SEVERA'
    ];
    
    unwantedUppercase.forEach(phrase => {
      const regex = new RegExp(phrase, 'gi');
      cleanedText = cleanedText.replace(regex, phrase.toLowerCase());
    });
    
    return cleanedText;
  };

  // Función para formatear el texto con estilos y resaltar escalas
  const formatInterpretationText = (text: string) => {
    // Limpiar el texto primero
    const cleanedText = cleanText(text);
    
    // Dividir el texto en párrafos
    const paragraphs = cleanedText.split('\n\n').filter(p => p.trim());

    return paragraphs.map((paragraph, index) => {
      // Textos que deben ir normales (sin formato especial)
      const normalTexts = [
        'Análisis completo de todos los Patrones de Personalidad',
        'Análisis completo de todas las Preocupaciones Expresadas',
        'Análisis completo de todos los Síndromes Clínicos',
        'Análisis completo de todas las Facetas de Grossman',
        'PRINCIPIOS GENERALES DE INTERPRETACIÓN PARA PUNTAJES PC',
        'PRINCIPIOS GENERALES DE INTERPRETACIÓN',
        'CONSIDERACIONES CLAVE ANTES DE INTERPRETAR'
      ];

      const isNormalText = normalTexts.some(text =>
        paragraph.includes(text) || paragraph.toUpperCase().includes(text.toUpperCase())
      );

      // Si es texto normal, renderizar sin formato especial PRIMERO
      if (isNormalText) {
        return (
          <Typography
            key={index}
            paragraph
            sx={{
              fontSize: '14px',
              lineHeight: 1.7,
              color: '#2c3e50',
              textAlign: 'left',
              marginBottom: '16px',
              fontWeight: 'normal',
              paddingLeft: '0px'
            }}
          >
            {paragraph}
          </Typography>
        );
      }

      // Detectar "RECOMENDACIONES CLÍNICAS"
      const isRecommendations = paragraph.toUpperCase().includes('RECOMENDACIONES CLÍNICAS');

      if (isRecommendations) {
        return (
          <Box
            key={index}
            className="section-title"
            sx={{
              backgroundColor: '#1e3a5f',
              color: 'white',
              padding: '12px 20px',
              margin: '24px 0 16px 0',
              borderRadius: '6px',
              textAlign: 'center',
              fontWeight: 'bold',
              fontSize: '16px',
              textTransform: 'uppercase',
              letterSpacing: '1px'
            }}
          >
            RECOMENDACIONES CLÍNICAS
          </Box>
        );
      }

      // Detectar título principal del reporte
      const isMainTitle = paragraph.toUpperCase().includes('INTERPRETACIÓN CLÍNICA PSICOLÓGICA');

      if (isMainTitle) {
        return (
          <Box
            key={index}
            className="main-title"
            sx={{
              backgroundColor: '#1e3a5f',
              color: 'white',
              padding: '16px 24px',
              margin: '0 0 24px 0',
              borderRadius: '8px',
              textAlign: 'center',
              fontWeight: 'bold',
              fontSize: '18px',
              textTransform: 'uppercase',
              letterSpacing: '1.5px',
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
            }}
          >
            INTERPRETACIÓN CLÍNICA PSICOLÓGICA
          </Box>
        );
      }

      // Detectar títulos de secciones principales
      const sectionTitles = [
        'PATRONES DE PERSONALIDAD',
        'PREOCUPACIONES EXPRESADAS',
        'SÍNDROMES CLÍNICOS'
      ];

      const isMainSection = sectionTitles.some(title =>
        paragraph.toUpperCase().includes(title) ||
        paragraph.includes(title)
      );

      if (isMainSection) {
        return (
          <Box
            key={index}
            className="section-title"
            sx={{
              backgroundColor: '#1e3a5f',
              color: 'white',
              padding: '12px 20px',
              margin: '24px 0 16px 0',
              borderRadius: '6px',
              textAlign: 'center',
              fontWeight: 'bold',
              fontSize: '16px',
              textTransform: 'uppercase',
              letterSpacing: '1px'
            }}
          >
            {paragraph.replace(/[:]/g, '').replace(/\([^)]*\)/g, '').trim()}
          </Box>
        );
      }

      // Detectar "FACETAS DE GROSSMAN" específicamente
      const isFacetasGrossman = paragraph.toUpperCase().includes('FACETAS DE GROSSMAN');

      if (isFacetasGrossman) {
        return (
          <Box
            key={index}
            className="section-title"
            sx={{
              backgroundColor: '#1e3a5f',
              color: 'white',
              padding: '12px 20px',
              margin: '24px 0 16px 0',
              borderRadius: '6px',
              textAlign: 'center',
              fontWeight: 'bold',
              fontSize: '16px',
              textTransform: 'uppercase',
              letterSpacing: '1px'
            }}
          >
            FACETAS DE GROSSMAN
          </Box>
        );
      }

      // Detectar otros títulos
      const isTitle = paragraph.includes(':') && paragraph.length < 100;

      if (isTitle) {
        return (
          <Typography
            key={index}
            variant="h6"
            sx={{
              color: '#1e3a5f',
              fontWeight: 'bold',
              marginTop: '24px',
              marginBottom: '12px',
              fontSize: '16px',
              borderLeft: '4px solid #1e3a5f',
              paddingLeft: '16px'
            }}
          >
            {paragraph}
          </Typography>
        );
      }

      // Función para resaltar escalas en el texto
      const highlightScales = (text: string) => {
        let highlightedText = text;

        // Patrones más específicos para cada tipo de escala
        const patterns = [
          // Patrones de personalidad: 1. Agraviado, 2. Conformista, etc.
          /(\b\d+\.\s+[A-Za-záéíóúñÑ]+)\s*\(PC[:\s]*(\d+)\)/g,
          // Patrones con letras: A. Difusión, B. Infravaloración, etc.
          /(\b[A-Z]\.\s+[A-Za-záéíóúñÑ\s]+)\s*\(PC[:\s]*(\d+)\)/g,
          // Patrones dobles: AA. Patrones, BB. Propensión, etc.
          /(\b[A-Z]{2}\.\s+[A-Za-záéíóúñÑ\s]+)\s*\(PC[:\s]*(\d+)\)/g,
          // Patrones con números y letras: 6A. Rebelde, 6B. Hostil, etc.
          /(\b[1-9][A-Z]\.\s+[A-Za-záéíóúñÑ\s]+)\s*\(PC[:\s]*(\d+)\)/g,
          // Patrones de Grossman: 1.1. Expresivamente, 1.2. Temperamentalmente, etc.
          /(\b\d+\.\d+\.\s+[A-Za-záéíóúñÑ\s]+)\s*\(PR[:\s]*(\d+)\)/g
        ];

        patterns.forEach(pattern => {
          highlightedText = highlightedText.replace(pattern, (match, scaleName, score) => {
            return `**${scaleName.trim()}** (**PC: ${score}**)`;
          });
        });

        return highlightedText;
      };

      // Aplicar resaltado de escalas
      const highlightedParagraph = highlightScales(paragraph);

      // Convertir markdown-style bold a JSX
      const renderTextWithBold = (text: string) => {
        const parts = text.split(/(\*\*[^*]+\*\*)/g);
        return parts.map((part, i) => {
          if (part.startsWith('**') && part.endsWith('**')) {
            const content = part.slice(2, -2);
            // Si contiene "PC:", hacer solo el PC en negrita
            if (content.includes('PC:')) {
              return (
                <span key={i} style={{ fontWeight: 'bold', color: '#1e3a5f' }}>
                  {content}
                </span>
              );
            }
            // Para nombres de escalas
            return (
              <span key={i} style={{ fontWeight: 'bold', color: '#2c3e50' }}>
                {content}
              </span>
            );
          }
          return part;
        });
      };

      // Detectar si es un párrafo de interpretación numerado
      const isNumberedInterpretation = /^\d+\.\s/.test(paragraph.trim());

      // Párrafo normal
      return (
        <Typography
          key={index}
          paragraph
          sx={{
            fontSize: '14px',
            lineHeight: 1.7,
            color: '#2c3e50',
            textAlign: 'left',
            marginBottom: '16px',
            textIndent: isNumberedInterpretation ? '0px' : '0px',
            paddingLeft: isNumberedInterpretation ? '0px' : '0px'
          }}
        >
          {renderTextWithBold(highlightedParagraph)}
        </Typography>
      );
    });
  };

  return (
    <Box sx={{ mb: 4 }}>
      <Paper
        elevation={2}
        sx={{
          padding: '32px',
          backgroundColor: '#fafafa',
          border: '1px solid #e0e0e0',
          borderRadius: '8px'
        }}
      >
        {/* Contenido de la interpretación */}
        <Box 
          className="qualitative-content"
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '6px',
            padding: '24px',
            border: '1px solid #e0e0e0',
            minHeight: '400px'
          }}
        >
          {formatInterpretationText(qualitativeInterpretation)}
        </Box>

        {/* Nota al pie */}
        <Divider sx={{ my: 3 }} />
        <Typography
          variant="caption"
          sx={{
            color: '#666',
            fontStyle: 'italic',
            textAlign: 'center',
            display: 'block',
            marginTop: '16px'
          }}
        >
          Esta interpretación se basa en los puntajes obtenidos en el MACI-II y debe ser considerada
          junto con otra información clínica relevante para una evaluación completa.
        </Typography>
      </Paper>
    </Box>
  );
};

export default React.memo(ImprovedQualitativeReport);