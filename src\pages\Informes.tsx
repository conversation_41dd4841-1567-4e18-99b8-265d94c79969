import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Container,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  CircularProgress,
  Alert,
  Dialog,
  DialogContent,
  DialogActions,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Chip
} from '@mui/material';
import {
  Description as ReportIcon,
  Person as PersonIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  CalendarToday as CalendarIcon,
  PictureAsPdf as PdfIcon
} from '@mui/icons-material';
// Redux imports removed - migrated to Zustand
// import { useSelector, useDispatch } from 'react-redux';
// import { RootState, AppDispatch } from '../store/store';
// import { fetchReports } from '../store/slices/reportsSlice';
import { ReportDialog } from '../components/ReportDialog';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { useReports, useReportUtils, useReportDialog, Report } from '../hooks/useReports';
import { useErrorHandler } from '../hooks/useErrorHandler';
import { reportFormatter } from '../utils/reportFormatters';
import '../styles/reportStyles.css';
import QualitativeReport from '../components/ReportDialog/QualitativeReport';

const Informes: React.FC = () => {
  // Migrated from Redux to custom hook
  const { reports, loading, error, selectedReport, setSelectedReport, deleteReport } = useReports();
  const { handleError } = useErrorHandler();
  const { formatDate } = useReportUtils();
  const {
    dialogOpen,
    handleViewReport: openDialog,
    handleCloseDialog,
    anchorEl,
    selectedReportForMenu,
    handleMenuClick,
    handleCloseMenu
  } = useReportDialog();

  const handleViewReport = useCallback((report: Report) => {
    setSelectedReport(report);
    openDialog(report);
  }, [setSelectedReport, openDialog]);
  
  const [tableStructureError, setTableStructureError] = useState<string | null>(null);
  const [showDatabaseSetup, setShowDatabaseSetup] = useState(false);
  // anchorEl and selectedReportForMenu are now managed by useReportDialog hook

  

  // Reports are automatically loaded by useReports hook
  // No need for manual useEffect with dispatch

  const handleDownloadPDF = useCallback(() => {
    if (!selectedReport) return;
    const originalTitle = document.title;
    const patientName = selectedReport.patient_name || 'Paciente';
    document.title = `Informe MACI-II - ${patientName}`;
    const printStyles = document.createElement('style');
    printStyles.id = 'pdf-print-styles';
    printStyles.textContent = `
      @media print {
        @page { margin: 0.5in; size: letter; }
        body * { visibility: hidden; }
        .report-content, .report-content * { visibility: visible; }
        .report-content {
          position: absolute; left: 0; top: 0; width: 100%;
          margin: 0; padding: 0.5in; background: white !important;
          max-width: 100%; box-sizing: border-box;
          transform: scale(0.95); transform-origin: top left;
        }
        .no-print, .MuiDialogActions-root, button { display: none !important; }
        * {
          print-color-adjust: exact !important;
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }

        /* Mantener colores de fondo y estilos */
        .main-title {
          background-color: #1e3a5f !important;
          color: white !important;
          padding: 16px 24px !important;
          border-radius: 8px !important;
          text-align: center !important;
          font-weight: bold !important;
        }

        .section-title {
          background-color: #1e3a5f !important;
          color: white !important;
          padding: 12px 20px !important;
          border-radius: 6px !important;
          text-align: center !important;
          font-weight: bold !important;
        }

        .MuiPaper-root {
          background: #f8f9fa !important;
          border: 1px solid #dee2e6 !important;
          border-radius: 8px !important;
        }

        .progress-bar {
          background-color: #f0f0f0 !important;
          border-radius: 4px !important;
        }

        .progress-fill {
          border-radius: 4px !important;
        }
      }
    `;
    document.head.appendChild(printStyles);
    window.print();
    setTimeout(() => {
      document.title = originalTitle;
      const styleElement = document.getElementById('pdf-print-styles');
      if (styleElement) styleElement.remove();
    }, 1000);
  }, [selectedReport]);

  // handleMenuClick and handleCloseMenu are now provided by useReportDialog hook

  const handleDeleteReport = useCallback(async (report: Report) => {
    try {
      await deleteReport(report.id);
      handleCloseMenu();
    } catch (err: any) {
      handleError(err as Error, {
        code: 'DELETE_REPORT_ERROR',
        details: { reportId: report.id }
      });
    }
  }, [deleteReport, handleCloseMenu, handleError]);

  const calculateAge = useCallback((birthDate: string): number => {
    if (!birthDate) return 0;
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  }, []);

  if (loading) {
    return (
      <ErrorBoundary>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress size={60} />
          </Box>
        </Container>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          Informes MACI-II
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Gestión y visualización de informes psicológicos generados
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {reports.length === 0 && !loading && (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '400px',
            textAlign: 'center'
          }}
        >
          <ReportIcon sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No hay informes disponibles
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Los informes generados aparecerán aquí
          </Typography>
        </Box>
      )}

      {reports.length > 0 && (
        <Grid container spacing={3}>
          {reports.map((report) => (
            <Grid item xs={12} sm={6} md={4} key={report.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  },
                  cursor: 'pointer'
                }}
                onClick={() => handleViewReport(report)}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <PersonIcon />
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="h2" noWrap>
                        {`${report.patient?.first_name || ''} ${report.patient?.last_name || ''}`.trim() || 'Paciente no especificado'}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {report.patient?.gender === 'M' ? 'Masculino' : report.patient?.gender === 'F' ? 'Femenino' : 'No especificado'} • {report.patient?.birth_date ? calculateAge(report.patient.birth_date) : 'No especificada'} años
                      </Typography>
                    </Box>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMenuClick(e, report);
                      }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>

                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>
                    {report.title}
                  </Typography>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <CalendarIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2" color="text.secondary">
                      {formatDate(report.created_at)}
                    </Typography>
                  </Box>

                  <Chip
                    label="Informe Completo"
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{ mt: 1 }}
                  />
                </CardContent>

                <CardActions sx={{ p: 2, pt: 0 }}>
                  <Button
                    size="small"
                    startIcon={<ViewIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewReport(report);
                    }}
                  >
                    Ver Informe
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
      >
        <MenuItem onClick={() => selectedReportForMenu && handleViewReport(selectedReportForMenu)}>
          <ViewIcon sx={{ mr: 1 }} />
          Ver Informe
        </MenuItem>
        <MenuItem 
          onClick={() => selectedReportForMenu && handleDeleteReport(selectedReportForMenu)}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Eliminar
        </MenuItem>
      </Menu>

      <ReportDialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        report={selectedReport}
        qualitative_interpretation={selectedReport?.qualitative_interpretation || ''}
        onDownloadPDF={handleDownloadPDF}
      />
    </Container>
    </ErrorBoundary>
  );
};

export default Informes;