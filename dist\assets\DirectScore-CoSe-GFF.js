import{r as P,q as z,j as e,B as a,C as O,A as M,v as F,ab as G,x as o,ac as H,ad as V,a1 as U,a2 as X,ae as q,af as Y,V as S,P as A,W as v,X as I,Y as u,T as t,Z as D,ag as Q,aa as Z,ah as _,ai as J}from"./index-ClZegq-Q.js";const K=k=>`Pregunta ${k}: Texto no disponible - por favor verifica la importación`,re=()=>{const[k,N]=P.useState([]),[T,L]=P.useState(!0),[W,$]=P.useState(null);if(z(),P.useEffect(()=>{(async()=>{try{L(!0),console.log("DirectScore: Iniciando obtención de pacientes");const r=await Q.getAll();if(!r.success||!r.data)throw new Error(r.message||"Error fetching patients");console.log(`DirectScore: Obtenidos ${r.data.length} pacientes`);const i=await Z.getAll();if(!i.success||!i.data)throw new Error(i.message||"Error fetching responses");console.log(`DirectScore: Obtenidas ${i.data.length} respuestas`);const x=r.data.map(n=>{var C;const b=((C=i.data)==null?void 0:C.filter(s=>s.patient_id===n.id))||[];console.log(`DirectScore: Paciente ${n.name} (${n.id}) tiene ${b.length} respuestas`);const d=b.length>0?b.reduce((s,y)=>{if(!y.answers||!Array.isArray(y.answers))return console.error("DirectScore: Respuestas inválidas",y),s;const g=y.answers.map((l,f)=>{let c=null;return l===!0||l===!1?c=l:typeof l=="number"?c=l>0:l==="true"?c=!0:l==="false"?c=!1:(l==null||console.error("DirectScore: Tipo de respuesta inesperado:",typeof l,String(l)),c=null),{id:f+1,answer:c}});try{Object.entries(_).forEach(([l,f])=>{if(!f||typeof f!="object"){console.error(`DirectScore: Categoría ${l} no tiene escalas válidas:`,f);return}Object.entries(f).forEach(([c,w])=>{if(!w||typeof w!="object"){console.error(`DirectScore: Escala ${c} no es válida:`,w);return}try{const h=w;s[c]||(s[c]={raw:0,direct:0,maxScore:h.maxScore||1,answers:{}});const R=J(g,h);console.log(`DirectScore: Escala ${c}, puntaje bruto = ${R}`),typeof R=="number"&&!isNaN(R)?s[c].raw+=R:console.error(`DirectScore: Puntaje no válido para escala ${c}:`,R),h.itemList&&Array.isArray(h.itemList)?h.itemList.forEach(j=>{const E=g.find(B=>B.id===j.id);E&&E.answer===(j.type==="true")&&(s[c].answers||(s[c].answers={}),s[c].answers[j.id]=!0,console.log(`DirectScore: Guardando respuesta verdadera para escala ${c}, item ${j.id}`))}):h.items&&Array.isArray(h.items)&&h.items.forEach(j=>{const E=g.find(B=>B.id===j);E&&E.answer===!0&&(s[c].answers||(s[c].answers={}),s[c].answers[j]=!0)})}catch(h){console.error(`DirectScore: Error al procesar escala ${c}:`,h)}})})}catch(l){console.error("DirectScore: Error al calcular puntajes:",l)}return s},{}):void 0;if(d){console.log("DirectScore: Convirtiendo puntajes brutos a directos");try{for(const s in d)if(Object.prototype.hasOwnProperty.call(d,s)){const y=d[s].raw,g=d[s].maxScore||1;if(typeof y!="number"||isNaN(y)){console.error(`DirectScore: Puntaje bruto inválido para escala ${s}:`,y),d[s].direct=0;continue}if(typeof g!="number"||isNaN(g)||g<=0){console.error(`DirectScore: Puntaje máximo inválido para escala ${s}:`,g),d[s].direct=0;continue}const l=y;isNaN(l)?(console.error(`DirectScore: Puntaje directo inválido para escala ${s}:`,{rawScore:y}),d[s].direct=0):(d[s].direct=l,console.log(`DirectScore: Escala ${s}, puntaje directo = ${d[s].direct}`),d[s].direct>d[s].maxScore&&(console.log(`DirectScore: Ajustando puntaje directo de ${s} de ${d[s].direct} a ${d[s].maxScore} (máximo)`),d[s].direct=d[s].maxScore))}}catch(s){console.error("DirectScore: Error al convertir puntajes:",s)}}else console.log("DirectScore: No hay puntajes para convertir");return{...n,scores:d}});console.log("DirectScore: Pacientes con puntajes procesados:",x.map(n=>({name:n.name,hasScores:!!n.scores,scalesCount:n.scores?Object.keys(n.scores).length:0}))),N(x)}catch(r){console.error("DirectScore: Error al obtener datos:",r),$(r.message||"Unknown error")}finally{L(!1)}})()},[]),T)return e.jsx(a,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:e.jsx(O,{})});if(W)return e.jsx(a,{p:3,children:e.jsx(M,{severity:"error",children:W})});const p={validityScales:{title:"Escalas de Validez",scales:[{id:"V",name:"Invalidez",abbr:"V",items:2,maxScore:2,itemList:[{id:60,type:"true"},{id:114,type:"false"}]},{id:"W",name:"Inconsistencia",abbr:"W",items:31,maxScore:31,pairsList:[{id1:28,type1:"false",id2:22,type2:"true"},{id1:28,type1:"true",id2:22,type2:"false"},{id1:72,type1:"true",id2:3,type2:"false"},{id1:75,type1:"false",id2:44,type2:"true"},{id1:79,type1:"false",id2:36,type2:"true"},{id1:83,type1:"false",id2:39,type2:"true"},{id1:84,type1:"true",id2:6,type2:"false"},{id1:85,type1:"false",id2:75,type2:"true"},{id1:86,type1:"false",id2:58,type2:"true"},{id1:90,type1:"false",id2:51,type2:"true"},{id1:103,type1:"true",id2:55,type2:"false"},{id1:108,type1:"true",id2:101,type2:"false"},{id1:115,type1:"true",id2:36,type2:"false"},{id1:117,type1:"true",id2:77,type2:"false"},{id1:117,type1:"false",id2:77,type2:"true"},{id1:122,type1:"true",id2:51,type2:"false"},{id1:124,type1:"false",id2:20,type2:"true"},{id1:124,type1:"true",id2:67,type2:"false"},{id1:125,type1:"false",id2:18,type2:"true"},{id1:125,type1:"true",id2:84,type2:"false"},{id1:127,type1:"false",id2:1,type2:"true"},{id1:127,type1:"true",id2:1,type2:"false"},{id1:129,type1:"true",id2:16,type2:"false"},{id1:134,type1:"true",id2:126,type2:"false"},{id1:135,type1:"false",id2:65,type2:"true"},{id1:144,type1:"false",id2:126,type2:"true"},{id1:155,type1:"false",id2:150,type2:"true"},{id1:157,type1:"true",id2:9,type2:"false"},{id1:158,type1:"false",id2:11,type2:"true"},{id1:158,type1:"true",id2:21,type2:"false"},{id1:159,type1:"true",id2:63,type2:"false"}]},{id:"X",name:"Negatividad de Respuesta",abbr:"X",items:42,maxScore:42,itemList:[{id:3,type:"true"},{id:17,type:"true"},{id:19,type:"true"},{id:22,type:"true"},{id:31,type:"true"},{id:32,type:"true"},{id:36,type:"true"},{id:39,type:"true"},{id:42,type:"true"},{id:44,type:"true"},{id:48,type:"true"},{id:70,type:"true"},{id:79,type:"true"},{id:81,type:"true"},{id:103,type:"true"},{id:109,type:"true"},{id:111,type:"true"},{id:112,type:"true"},{id:115,type:"true"},{id:117,type:"true"},{id:120,type:"true"},{id:129,type:"true"},{id:133,type:"true"},{id:134,type:"true"},{id:142,type:"true"},{id:144,type:"true"},{id:148,type:"true"},{id:155,type:"true"},{id:157,type:"true"},{id:5,type:"false"},{id:12,type:"false"},{id:33,type:"false"},{id:43,type:"false"},{id:50,type:"false"},{id:56,type:"false"},{id:73,type:"false"},{id:99,type:"false"},{id:101,type:"false"},{id:108,type:"false"},{id:121,type:"false"},{id:140,type:"false"},{id:146,type:"false"}]}]},personalityPatterns:{title:"Escalas de Patrones de Personalidad",scales:[{id:"1",name:"Introvertido",abbr:"INT",items:13,maxScore:19},{id:"2",name:"Inhibido",abbr:"INH",items:13,maxScore:19},{id:"3",name:"Sumiso",abbr:"SUB",items:12,maxScore:19},{id:"4",name:"Dramatizador",abbr:"DRA",items:14,maxScore:21},{id:"5",name:"Egocéntrico",abbr:"EGO",items:15,maxScore:23},{id:"6A",name:"Indisciplinado",abbr:"UNR",items:11,maxScore:17},{id:"6B",name:"Dominante",abbr:"FOR",items:13,maxScore:21},{id:"7",name:"Conformista",abbr:"CON",items:17,maxScore:24},{id:"8A",name:"Descontento",abbr:"DIS",items:16,maxScore:23},{id:"8B",name:"Agraviado",abbr:"AGG",items:14,maxScore:21},{id:"9",name:"Tendencia Limítrofe",abbr:"BTE",items:17,maxScore:24}]},expressedConcerns:{title:"Escalas de Preocupaciones Expresadas",scales:[{id:"A",name:"Difusión de Identidad",abbr:"ID",items:10,maxScore:15},{id:"B",name:"Autodevaluación",abbr:"SD",items:10,maxScore:15},{id:"C",name:"Inseguridad entre Compañeros",abbr:"PI",items:9,maxScore:13},{id:"D",name:"Discordia Familiar",abbr:"FD",items:11,maxScore:16}]},clinicalSyndromes:{title:"Escalas de Síndromes Clínicos",scales:[{id:"AA",name:"Patrón de Atracones Alimenticios",abbr:"BE",items:7,maxScore:14},{id:"BB",name:"Propensión al Abuso de Sustancias",abbr:"SA",items:8,maxScore:16},{id:"CC",name:"Predisposición Delincuente",abbr:"DP",items:10,maxScore:15},{id:"DD",name:"Sentimientos Ansiosos",abbr:"AF",items:13,maxScore:20},{id:"EE",name:"Afecto Depresivo",abbr:"DA",items:15,maxScore:21},{id:"FF",name:"Tendencia Suicida",abbr:"ST",items:9,maxScore:14},{id:"GG",name:"Desregulación del Estado de Ánimo Disruptivo",abbr:"DM",items:12,maxScore:19},{id:"HH",name:"Estrés Postraumático",abbr:"PT",items:11,maxScore:17},{id:"II",name:"Distorsiones de la Realidad",abbr:"RD",items:13,maxScore:19}]},grossmanFacets:{title:"Escalas de Facetas de Grossman",scales:[{id:"1.1",personality:"Introversivo",name:"Expresivamente Impasible",abbr:"1.1",items:6,maxScore:6},{id:"1.2",personality:"Introversivo",name:"Temperamentalmente Apático",abbr:"1.2",items:7,maxScore:7},{id:"1.3",personality:"Introversivo",name:"Interpersonalmente Desvinculado",abbr:"1.3",items:8,maxScore:8},{id:"2.1",personality:"Inhibido",name:"Expresivamente Inquieto",abbr:"2.1",items:13,maxScore:13},{id:"2.2",personality:"Inhibido",name:"Interpersonalmente Aversivo",abbr:"2.2",items:11,maxScore:11},{id:"2.3",personality:"Inhibido",name:"Autoimagen Alienada",abbr:"2.3",items:10,maxScore:10},{id:"3.1",personality:"Sumiso",name:"Interpersonalmente Dócil",abbr:"3.1",items:10,maxScore:10},{id:"3.2",personality:"Sumiso",name:"Temperamentalmente Pacifista",abbr:"3.2",items:11,maxScore:11},{id:"3.3",personality:"Sumiso",name:"Expresivamente Incompetente",abbr:"3.3",items:13,maxScore:13},{id:"4.1",personality:"Dramatizante",name:"Interpersonalmente Buscador de Atención",abbr:"4.1",items:9,maxScore:9},{id:"4.2",personality:"Dramatizante",name:"Autoimagen Gregaria",abbr:"4.2",items:10,maxScore:10},{id:"4.3",personality:"Dramatizante",name:"Temperamentalmente Voluble",abbr:"4.3",items:8,maxScore:8},{id:"5.1",personality:"Egoísta",name:"Autoimagen Admirable",abbr:"5.1",items:12,maxScore:12},{id:"5.2",personality:"Egoísta",name:"Cognitivamente Expansivo",abbr:"5.2",items:11,maxScore:11},{id:"5.3",personality:"Egoísta",name:"Interpersonalmente Explotador",abbr:"5.3",items:9,maxScore:9},{id:"6A.1",personality:"Indisciplinado",name:"Expresivamente Impulsivo",abbr:"6A.1",items:14,maxScore:14},{id:"6A.2",personality:"Indisciplinado",name:"Mecanismo de Actuar",abbr:"6A.2",items:12,maxScore:12},{id:"6A.3",personality:"Indisciplinado",name:"Interpersonalmente Irresponsable",abbr:"6A.3",items:10,maxScore:10},{id:"6B.1",personality:"Imponente",name:"Interpersonalmente Abrasivo",abbr:"6B.1",items:11,maxScore:11},{id:"6B.2",personality:"Imponente",name:"Expresivamente Precipitado",abbr:"6B.2",items:13,maxScore:13},{id:"6B.3",personality:"Imponente",name:"Temperamentalmente Hostil",abbr:"6B.3",items:10,maxScore:10},{id:"7.1",personality:"Conformista",name:"Expresivamente Disciplinado",abbr:"7.1",items:12,maxScore:12},{id:"7.2",personality:"Conformista",name:"Interpersonalmente Respetuoso",abbr:"7.2",items:14,maxScore:14},{id:"7.3",personality:"Conformista",name:"Autoimagen Concienzuda",abbr:"7.3",items:11,maxScore:11},{id:"8A.1",personality:"Descontento",name:"Autoimagen Disconforme",abbr:"8A.1",items:13,maxScore:13},{id:"8A.2",personality:"Descontento",name:"Expresivamente Resentido",abbr:"8A.2",items:12,maxScore:12},{id:"8A.3",personality:"Descontento",name:"Interpersonalmente Contrario",abbr:"8A.3",items:10,maxScore:10},{id:"8B.1",personality:"Agraviado",name:"Cognitivamente Indeciso",abbr:"8B.1",items:13,maxScore:13},{id:"8B.2",personality:"Agraviado",name:"Autoimagen No Merecedor",abbr:"8B.2",items:12,maxScore:12},{id:"8B.3",personality:"Agraviado",name:"Temperamentalmente Disfórico",abbr:"8B.3",items:10,maxScore:10},{id:"9.1",personality:"Tendencia Límite",name:"Temperamentalmente Lábil",abbr:"9.1",items:13,maxScore:13},{id:"9.2",personality:"Tendencia Límite",name:"Interpersonalmente Paradójico",abbr:"9.2",items:12,maxScore:12},{id:"9.3",personality:"Tendencia Límite",name:"Autoimagen Incierta",abbr:"9.3",items:10,maxScore:10}]},outstandingResponses:{title:"Respuestas Destacadas",scales:[{id:"R1",name:"Propenso a la Venganza",abbr:"R1",items:3,maxScore:3,itemList:[{id:74,type:"true"},{id:120,type:"true"},{id:151,type:"true"}]},{id:"R2",name:"Pensamientos Suicidas",abbr:"R2",items:6,maxScore:6,itemList:[{id:36,type:"true"},{id:79,type:"true"},{id:103,type:"true"},{id:115,type:"true"},{id:143,type:"true"},{id:156,type:"true"}]},{id:"R3",name:"Autolesiones No Suicidas",abbr:"R3",items:2,maxScore:2,itemList:[{id:19,type:"true"},{id:55,type:"true"}]},{id:"R4",name:"Experiencias Traumáticas",abbr:"R4",items:4,maxScore:4,itemList:[{id:18,type:"true"},{id:68,type:"true"},{id:84,type:"true"},{id:139,type:"true"}]},{id:"R5",name:"Lapsos en la Prueba de Realidad",abbr:"R5",items:5,maxScore:5,itemList:[{id:15,type:"true"},{id:70,type:"true"},{id:78,type:"true"},{id:111,type:"true"},{id:145,type:"true"}]},{id:"R6",name:"Desaliento/Desesperación",abbr:"R6",items:4,maxScore:4,itemList:[{id:2,type:"true"},{id:44,type:"true"},{id:77,type:"true"},{id:117,type:"true"}]},{id:"R7",name:"Espectro Bipolar",abbr:"R7",items:3,maxScore:3,itemList:[{id:14,type:"true"},{id:53,type:"true"},{id:142,type:"true"}]},{id:"R8",name:"Ira Explosiva",abbr:"R8",items:3,maxScore:3,itemList:[{id:45,type:"true"},{id:150,type:"true"},{id:155,type:"true"}]},{id:"R9",name:"Problemas de Control de Impulsos",abbr:"R9",items:2,maxScore:2,itemList:[{id:4,type:"true"},{id:17,type:"true"}]},{id:"R10",name:"Ira Instrumental",abbr:"R10",items:3,maxScore:3,itemList:[{id:52,type:"true"},{id:109,type:"true"},{id:149,type:"true"}]},{id:"R11",name:"Uso de Alcohol/Drogas",abbr:"R11",items:2,maxScore:2,itemList:[{id:11,type:"true"},{id:112,type:"true"}]},{id:"R12",name:"Preocupaciones Alimentarias",abbr:"R12",items:3,maxScore:3,itemList:[{id:57,type:"true"},{id:97,type:"true"},{id:157,type:"true"}]}]}};return e.jsxs(F,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(a,{sx:{display:"flex",alignItems:"center",mb:4,justifyContent:"flex-start"},children:[e.jsx(G,{sx:{fontSize:40,color:"#1976d2",mr:2}}),e.jsx(o,{variant:"h4",sx:{color:"black",fontWeight:"bold"},children:"Puntaje Directo Pacientes"})]}),k.map(m=>e.jsxs(H,{defaultExpanded:!1,sx:{mb:2,boxShadow:"0 2px 4px rgba(0,0,0,0.1)","&:hover":{boxShadow:"0 4px 8px rgba(0,0,0,0.15)",transform:"translateY(-2px)"},backgroundColor:m.gender==="M"?"#e3f2fd":"#fce4ec",borderRadius:"8px",overflow:"hidden",transition:"transform 0.2s, box-shadow 0.2s"},children:[e.jsx(V,{expandIcon:e.jsx(q,{}),sx:{backgroundColor:m.gender==="M"?"#bbdefb":"#f8bbd0","&:hover":{backgroundColor:m.gender==="M"?"#90caf9":"#f48fb1"}},children:e.jsx(a,{display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",pr:2,children:e.jsxs(a,{display:"flex",alignItems:"center",children:[m.gender==="M"?e.jsx(U,{sx:{mr:1,verticalAlign:"middle",color:"#1976d2",fontSize:"1.8rem"}}):e.jsx(X,{sx:{mr:1,verticalAlign:"middle",color:"#d81b60",fontSize:"1.8rem"}}),e.jsx(o,{variant:"h6",sx:{fontWeight:500},children:m.name})]})})}),e.jsx(Y,{children:m.scores?e.jsxs(a,{children:[e.jsxs(a,{mb:4,children:[e.jsx(o,{variant:"h5",gutterBottom:!0,sx:{backgroundColor:"#1976d2",color:"white",p:1,fontWeight:"bold",textAlign:"center",borderRadius:"8px"},children:p.validityScales.title}),e.jsx(S,{component:A,elevation:3,children:e.jsxs(v,{size:"small",children:[e.jsx(I,{sx:{backgroundColor:r=>r.palette.grey[200]},children:e.jsxs(u,{children:[e.jsx(t,{align:"center",children:"Escala"}),e.jsx(t,{align:"center",children:"Nombre"}),e.jsx(t,{align:"center",children:"Número de Items"}),e.jsx(t,{align:"center",children:"Puntuación Máxima"}),e.jsx(t,{align:"center",children:"Puntaje Obtenido"})]})}),e.jsx(D,{children:p.validityScales.scales.map(r=>{var n;const i=(n=m.scores)==null?void 0:n[r.abbr],x=i&&typeof i.direct=="number"&&!isNaN(i.direct)?i.direct:0;return e.jsxs(u,{children:[e.jsx(t,{align:"center",children:e.jsx(a,{sx:{width:36,height:36,borderRadius:"50%",backgroundColor:"#009688",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",mx:"auto"},children:r.id})}),e.jsx(t,{align:"center",children:r.name}),e.jsx(t,{align:"center",sx:{color:"black"},children:r.items}),e.jsx(t,{align:"center",sx:{color:"black"},children:r.maxScore}),e.jsx(t,{align:"center",children:e.jsx(o,{sx:{fontWeight:"bold",color:"red"},children:x})})]},r.abbr)})})]})})]}),e.jsxs(a,{mb:4,children:[e.jsx(o,{variant:"h5",gutterBottom:!0,sx:{backgroundColor:"#1976d2",color:"white",p:1,fontWeight:"bold",textAlign:"center",borderRadius:"8px"},children:p.personalityPatterns.title}),e.jsx(S,{component:A,elevation:3,children:e.jsxs(v,{size:"small",children:[e.jsx(I,{sx:{backgroundColor:r=>r.palette.grey[200]},children:e.jsxs(u,{children:[e.jsx(t,{align:"center",children:"Escala"}),e.jsx(t,{align:"center",children:"Nombre"}),e.jsx(t,{align:"center",children:"Número de Items"}),e.jsx(t,{align:"center",children:"Puntuación Máxima"}),e.jsx(t,{align:"center",children:"Puntaje Obtenido"})]})}),e.jsx(D,{children:p.personalityPatterns.scales.map(r=>{var n;const i=(n=m.scores)==null?void 0:n[r.abbr],x=i&&typeof i.direct=="number"&&!isNaN(i.direct)?i.direct:0;return e.jsxs(u,{children:[e.jsx(t,{align:"center",children:e.jsxs(a,{display:"flex",alignItems:"center",children:[e.jsx(a,{sx:{width:36,height:36,borderRadius:"50%",backgroundColor:"#009688",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",mr:1},children:r.id}),e.jsx(o,{children:r.name})]})}),e.jsx(t,{align:"center",sx:{color:"#1976d2"},children:r.abbr}),e.jsx(t,{align:"center",sx:{color:"black"},children:r.items}),e.jsx(t,{align:"center",sx:{color:"black"},children:r.maxScore}),e.jsx(t,{align:"center",children:e.jsx(o,{sx:{fontWeight:"bold",color:"red"},children:x})})]},r.abbr)})})]})})]}),e.jsxs(a,{mb:4,children:[e.jsx(o,{variant:"h5",gutterBottom:!0,sx:{backgroundColor:"#1976d2",color:"white",p:1,fontWeight:"bold",textAlign:"center",borderRadius:"8px"},children:p.expressedConcerns.title}),e.jsx(S,{component:A,elevation:3,children:e.jsxs(v,{size:"small",children:[e.jsx(I,{sx:{backgroundColor:r=>r.palette.grey[200]},children:e.jsxs(u,{children:[e.jsx(t,{align:"center",children:"Escala"}),e.jsx(t,{align:"center",children:"Nombre"}),e.jsx(t,{align:"center",children:"Número de Items"}),e.jsx(t,{align:"center",children:"Puntuación Máxima"}),e.jsx(t,{align:"center",children:"Puntaje Obtenido"})]})}),e.jsx(D,{children:p.expressedConcerns.scales.map(r=>{var n;const i=(n=m.scores)==null?void 0:n[r.abbr],x=i&&typeof i.direct=="number"&&!isNaN(i.direct)?i.direct:0;return e.jsxs(u,{children:[e.jsx(t,{align:"center",children:e.jsxs(a,{display:"flex",alignItems:"center",children:[e.jsx(a,{sx:{width:36,height:36,borderRadius:"50%",backgroundColor:"#009688",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",mr:1},children:r.id}),e.jsx(o,{children:r.name})]})}),e.jsx(t,{align:"center",sx:{color:"#1976d2"},children:r.abbr}),e.jsx(t,{align:"center",sx:{color:"black"},children:r.items}),e.jsx(t,{align:"center",sx:{color:"black"},children:r.maxScore}),e.jsx(t,{align:"center",children:e.jsx(o,{sx:{fontWeight:"bold",color:"red"},children:x})})]},r.abbr)})})]})})]}),e.jsxs(a,{mb:4,children:[e.jsx(o,{variant:"h5",gutterBottom:!0,sx:{backgroundColor:"#1976d2",color:"white",p:1,fontWeight:"bold",textAlign:"center",borderRadius:"8px"},children:p.clinicalSyndromes.title}),e.jsx(S,{component:A,elevation:3,children:e.jsxs(v,{size:"small",children:[e.jsx(I,{sx:{backgroundColor:r=>r.palette.grey[200]},children:e.jsxs(u,{children:[e.jsx(t,{align:"center",children:"Escala"}),e.jsx(t,{align:"center",children:"Nombre"}),e.jsx(t,{align:"center",children:"Número de Items"}),e.jsx(t,{align:"center",children:"Puntuación Máxima"}),e.jsx(t,{align:"center",children:"Puntaje Obtenido"})]})}),e.jsx(D,{children:p.clinicalSyndromes.scales.map(r=>{var n;const i=(n=m.scores)==null?void 0:n[r.abbr],x=i&&typeof i.direct=="number"&&!isNaN(i.direct)?i.direct:0;return e.jsxs(u,{children:[e.jsx(t,{align:"center",children:e.jsxs(a,{display:"flex",alignItems:"center",children:[e.jsx(a,{sx:{width:36,height:36,borderRadius:"50%",backgroundColor:"#009688",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",mr:1},children:r.id}),e.jsx(o,{children:r.name})]})}),e.jsx(t,{align:"center",sx:{color:"#1976d2"},children:r.abbr}),e.jsx(t,{align:"center",sx:{color:"black"},children:r.items}),e.jsx(t,{align:"center",sx:{color:"black"},children:r.maxScore}),e.jsx(t,{align:"center",children:e.jsx(o,{sx:{fontWeight:"bold",color:"red"},children:x})})]},r.abbr)})})]})})]}),e.jsxs(a,{mb:4,mt:6,children:[e.jsx(o,{variant:"h5",gutterBottom:!0,sx:{backgroundColor:"#1976d2",color:"white",p:1,fontWeight:"bold",textAlign:"center",borderRadius:"8px"},children:p.grossmanFacets.title}),Array.from(new Set(p.grossmanFacets.scales.map(r=>r.personality))).map(r=>{const i=p.grossmanFacets.scales.filter(n=>n.personality===r),x=i[0].id.split(".")[0];return e.jsxs(a,{mb:3,sx:{backgroundColor:"#f5f5f5",borderRadius:"8px",overflow:"hidden"},children:[e.jsxs(a,{sx:{backgroundColor:"#009688",p:1,display:"flex",alignItems:"center"},children:[e.jsx(a,{sx:{width:36,height:36,borderRadius:"50%",backgroundColor:"white",color:"#009688",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",mr:1},children:x}),e.jsx(o,{variant:"h6",sx:{color:"white",fontWeight:"bold"},children:r})]}),e.jsx(S,{component:A,elevation:0,sx:{backgroundColor:"transparent"},children:e.jsxs(v,{size:"small",children:[e.jsx(I,{sx:{backgroundColor:n=>n.palette.grey[200]},children:e.jsxs(u,{children:[e.jsx(t,{children:"Escala"}),e.jsx(t,{children:"Nombre"}),e.jsx(t,{align:"center",children:"Número de Items"}),e.jsx(t,{align:"center",children:"Puntuación Máxima"}),e.jsx(t,{align:"center",children:"Puntaje Obtenido"})]})}),e.jsx(D,{children:i.map(n=>{var C;const b=(C=m.scores)==null?void 0:C[n.abbr],d=b&&typeof b.direct=="number"&&!isNaN(b.direct)?b.direct:0;return e.jsxs(u,{children:[e.jsx(t,{children:e.jsx(o,{sx:{fontWeight:"bold",color:"#009688"},children:n.id})}),e.jsx(t,{children:e.jsx(o,{children:n.name})}),e.jsx(t,{align:"center",sx:{color:"black"},children:n.items}),e.jsx(t,{align:"center",sx:{color:"black"},children:n.maxScore}),e.jsx(t,{align:"center",children:e.jsx(o,{sx:{fontWeight:"bold",color:"red"},children:d})})]},n.id)})})]})})]},r)})]}),e.jsxs(a,{mb:4,children:[e.jsx(o,{variant:"h5",gutterBottom:!0,sx:{backgroundColor:"#1976d2",color:"white",p:1,fontWeight:"bold",textAlign:"center",borderRadius:"8px"},children:p.outstandingResponses.title}),e.jsx(S,{component:A,elevation:3,children:e.jsxs(v,{size:"small",children:[e.jsx(I,{sx:{backgroundColor:r=>r.palette.grey[200]},children:e.jsxs(u,{children:[e.jsx(t,{align:"center",width:"20%",children:"Escala"}),e.jsx(t,{align:"center",width:"50%",children:"Respuestas"}),e.jsx(t,{align:"center",width:"10%",children:"Número de Items"}),e.jsx(t,{align:"center",width:"10%",children:"Puntuación Máxima"}),e.jsx(t,{align:"center",width:"10%",children:"Puntaje Obtenido"})]})}),e.jsx(D,{children:p.outstandingResponses.scales.map(r=>{var n;const i=(n=m.scores)==null?void 0:n[r.abbr],x=i&&typeof i.direct=="number"&&!isNaN(i.direct)?i.direct:0;return e.jsxs(u,{children:[e.jsx(t,{children:e.jsxs(a,{sx:{display:"flex",alignItems:"center"},children:[e.jsx(a,{sx:{width:36,height:36,borderRadius:"50%",backgroundColor:"#009688",color:"white",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",mr:1},children:r.id}),e.jsx(o,{sx:{fontWeight:"bold"},children:r.name})]})}),e.jsx(t,{children:r.itemList&&Array.isArray(r.itemList)&&(i!=null&&i.answers)?e.jsx(a,{children:r.itemList.map(b=>{var d;return((d=i.answers)==null?void 0:d[b.id])===!0?e.jsxs(a,{sx:{mb:1.5},children:[e.jsx(o,{variant:"body2",sx:{mb:.5},children:e.jsxs(a,{component:"span",sx:{fontWeight:"bold",color:"#009688"},children:["Pregunta ",b.id,":"]})}),e.jsx(o,{variant:"body2",sx:{pl:1,borderLeft:"4px solid #009688",py:.5},children:K(b.id)})]},b.id.toString()):null})}):e.jsx(o,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:"No hay respuestas destacadas"})}),e.jsx(t,{align:"center",children:r.items}),e.jsx(t,{align:"center",children:r.maxScore}),e.jsx(t,{align:"center",children:e.jsx(o,{sx:{fontWeight:"bold",color:"red"},children:x})})]},r.abbr)})})]})})]})]}):e.jsx(o,{children:"No hay respuestas de cuestionario para este paciente."})})]},m.id))]})};export{re as default};
