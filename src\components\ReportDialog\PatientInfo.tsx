// Componente para mostrar información del paciente
// Separado para mejorar la modularidad y reutilización

import React from 'react';
import { Box, Typography, Grid } from '@mui/material';
import { Report } from '../../hooks/useReports';

interface PatientInfoProps {
  report: Report;
  calculateAge: (birthDate: string, testDate: string) => number;
  formatDate: (date: string) => string;
}

const PatientInfo: React.FC<PatientInfoProps> = ({ 
  report, 
  calculateAge, 
  formatDate 
}) => {
  // Obtener datos del paciente con compatibilidad
  const patientName = report.patient_name || 
    `${report.patient?.first_name || ''} ${report.patient?.last_name || ''}`.trim() || 
    'No especificado';
  
  const birthDate = report.patient_birth_date || report.patient?.birth_date || '';
  const testDate = report.test_date || report.created_at || '';
  const gender = report.patient?.gender || report.gender || '';
  
  return (
    <Box
      className="patient-info"
      sx={{
        background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
        padding: '32px',
        borderRadius: '12px',
        border: '1px solid #e3f2fd',
        marginBottom: '32px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: 'linear-gradient(90deg, #1e3a5f 0%, #048799 100%)',
          borderRadius: '12px 12px 0 0'
        }
      }}
    >
      <Grid container spacing={4}>
        <Grid item xs={12} sm={6}>
          <Box sx={{ mb: 3 }}>
            <Typography 
              variant="body1" 
              sx={{ 
                fontWeight: '600',
                color: '#1e3a5f',
                fontSize: '14px',
                mb: 1,
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              Nombre:
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: '#2c3e50',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: '#f8f9fa',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}
            >
              {patientName}
            </Typography>
          </Box>
          
          <Box sx={{ mb: 3 }}>
            <Typography 
              variant="body1" 
              sx={{ 
                fontWeight: '600',
                color: '#1e3a5f',
                fontSize: '14px',
                mb: 1,
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              Fecha de Nacimiento:
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: '#2c3e50',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: '#f8f9fa',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}
            >
              {birthDate ? formatDate(birthDate) : 'Invalid Date'}
            </Typography>
          </Box>
          
          <Box sx={{ mb: 3 }}>
            <Typography 
              variant="body1" 
              sx={{ 
                fontWeight: '600',
                color: '#1e3a5f',
                fontSize: '14px',
                mb: 1,
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              Edad:
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: '#2c3e50',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: '#f8f9fa',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}
            >
              {birthDate ? calculateAge(birthDate, testDate) : 0} años
            </Typography>
          </Box>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Box sx={{ mb: 3 }}>
            <Typography 
              variant="body1" 
              sx={{ 
                fontWeight: '600',
                color: '#1e3a5f',
                fontSize: '14px',
                mb: 1,
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              Género:
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: '#2c3e50',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: '#f8f9fa',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}
            >
              {gender === 'M' ? 'Masculino' : gender === 'F' ? 'Femenino' : 'No especificado'}
            </Typography>
          </Box>
          
          <Box sx={{ mb: 3 }}>
            <Typography 
              variant="body1" 
              sx={{ 
                fontWeight: '600',
                color: '#1e3a5f',
                fontSize: '14px',
                mb: 1,
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              Fecha de Evaluación:
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: '#2c3e50',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: '#f8f9fa',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}
            >
              {testDate ? formatDate(testDate) : 'Invalid Date'}
            </Typography>
          </Box>
          
          <Box sx={{ mb: 3 }}>
            <Typography 
              variant="body1" 
              sx={{ 
                fontWeight: '600',
                color: '#1e3a5f',
                fontSize: '14px',
                mb: 1,
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              Evaluador:
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: '#2c3e50',
                fontSize: '14px',
                fontWeight: '500',
                backgroundColor: '#f8f9fa',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}
            >
              No especificado
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default React.memo(PatientInfo);