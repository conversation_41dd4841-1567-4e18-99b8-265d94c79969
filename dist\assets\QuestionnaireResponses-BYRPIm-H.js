import{c as dt,j as r,r as S,_ as ht,u as bt,I as U,F as pt,L as wt,a as P,g as yt,b as vt,d as jt,T as V,e as Ve,s as se,f as Re,h as Pt,i as Mt,k as Ct,M as N,l as St,S as we,m as je,n as Pe,o as Me,p as Ce,q as kt,t as Dt,B as C,C as Rt,A as It,v as Bt,w as Tt,P as ce,x as D,y as Ot,z as Ee,D as Le,E as Ft,G as qe,H as Wt,J as $e,K as _t,N as Et,O as A,Q as Ie,R as Be,U as Te,V as Lt,W as qt,X as Yt,Y as Ge,Z as At,$ as Nt,a0 as zt,a1 as Xe,a2 as Ue,a3 as Ye,a4 as Je,a5 as Ht,a6 as Qt,a7 as Vt,a8 as Oe,a9 as $t,aa as Gt}from"./index-ClZegq-Q.js";import{C as Xt}from"./CardActions-DPvsgKyJ.js";import{a as Ae}from"./colorManipulator-DcXpBuvL.js";const Ut=dt(r.jsx("path",{d:"M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"}),"KeyboardArrowLeft"),Jt=dt(r.jsx("path",{d:"M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"}),"KeyboardArrowRight"),Kt=["backIconButtonProps","count","disabled","getItemAriaLabel","nextIconButtonProps","onPageChange","page","rowsPerPage","showFirstButton","showLastButton","slots","slotProps"],Zt=S.forwardRef(function(e,n){var a,s,i,l,c,m,x,g;const{backIconButtonProps:R,count:I,disabled:v=!1,getItemAriaLabel:h,nextIconButtonProps:y,onPageChange:_,page:k,rowsPerPage:q,showFirstButton:H,showLastButton:$,slots:Y={},slotProps:j={}}=e,oe=ht(e,Kt),M=bt(),de=p=>{_(p,0)},G=p=>{_(p,k-1)},K=p=>{_(p,k+1)},b=p=>{_(p,Math.max(0,Math.ceil(I/q)-1))},F=(a=Y.firstButton)!=null?a:U,Z=(s=Y.lastButton)!=null?s:U,ee=(i=Y.nextButton)!=null?i:U,te=(l=Y.previousButton)!=null?l:U,ne=(c=Y.firstButtonIcon)!=null?c:pt,ie=(m=Y.lastButtonIcon)!=null?m:wt,E=(x=Y.nextButtonIcon)!=null?x:Jt,le=(g=Y.previousButtonIcon)!=null?g:Ut,_e=M?Z:F,Se=M?ee:te,ae=M?te:ee,ke=M?F:Z,De=M?j.lastButton:j.firstButton,o=M?j.nextButton:j.previousButton,d=M?j.previousButton:j.nextButton,u=M?j.firstButton:j.lastButton;return r.jsxs("div",P({ref:n},oe,{children:[H&&r.jsx(_e,P({onClick:de,disabled:v||k===0,"aria-label":h("first",k),title:h("first",k)},De,{children:M?r.jsx(ie,P({},j.lastButtonIcon)):r.jsx(ne,P({},j.firstButtonIcon))})),r.jsx(Se,P({onClick:G,disabled:v||k===0,color:"inherit","aria-label":h("previous",k),title:h("previous",k)},o??R,{children:M?r.jsx(E,P({},j.nextButtonIcon)):r.jsx(le,P({},j.previousButtonIcon))})),r.jsx(ae,P({onClick:K,disabled:v||(I!==-1?k>=Math.ceil(I/q)-1:!1),color:"inherit","aria-label":h("next",k),title:h("next",k)},d??y,{children:M?r.jsx(le,P({},j.previousButtonIcon)):r.jsx(E,P({},j.nextButtonIcon))})),$&&r.jsx(ke,P({onClick:b,disabled:v||k>=Math.ceil(I/q)-1,"aria-label":h("last",k),title:h("last",k)},u,{children:M?r.jsx(ne,P({},j.firstButtonIcon)):r.jsx(ie,P({},j.lastButtonIcon))}))]}))});function en(t){return vt("MuiTablePagination",t)}const ye=yt("MuiTablePagination",["root","toolbar","spacer","selectLabel","selectRoot","select","selectIcon","input","menuItem","displayedRows","actions"]);var Ke;const tn=["ActionsComponent","backIconButtonProps","className","colSpan","component","count","disabled","getItemAriaLabel","labelDisplayedRows","labelRowsPerPage","nextIconButtonProps","onPageChange","onRowsPerPageChange","page","rowsPerPage","rowsPerPageOptions","SelectProps","showFirstButton","showLastButton","slotProps","slots"],nn=se(V,{name:"MuiTablePagination",slot:"Root",overridesResolver:(t,e)=>e.root})(({theme:t})=>({overflow:"auto",color:(t.vars||t).palette.text.primary,fontSize:t.typography.pxToRem(14),"&:last-child":{padding:0}})),an=se(St,{name:"MuiTablePagination",slot:"Toolbar",overridesResolver:(t,e)=>P({[`& .${ye.actions}`]:e.actions},e.toolbar)})(({theme:t})=>({minHeight:52,paddingRight:2,[`${t.breakpoints.up("xs")} and (orientation: landscape)`]:{minHeight:52},[t.breakpoints.up("sm")]:{minHeight:52,paddingRight:2},[`& .${ye.actions}`]:{flexShrink:0,marginLeft:20}})),rn=se("div",{name:"MuiTablePagination",slot:"Spacer",overridesResolver:(t,e)=>e.spacer})({flex:"1 1 100%"}),sn=se("p",{name:"MuiTablePagination",slot:"SelectLabel",overridesResolver:(t,e)=>e.selectLabel})(({theme:t})=>P({},t.typography.body2,{flexShrink:0})),on=se(we,{name:"MuiTablePagination",slot:"Select",overridesResolver:(t,e)=>P({[`& .${ye.selectIcon}`]:e.selectIcon,[`& .${ye.select}`]:e.select},e.input,e.selectRoot)})({color:"inherit",fontSize:"inherit",flexShrink:0,marginRight:32,marginLeft:8,[`& .${ye.select}`]:{paddingLeft:8,paddingRight:24,textAlign:"right",textAlignLast:"right"}}),ln=se(N,{name:"MuiTablePagination",slot:"MenuItem",overridesResolver:(t,e)=>e.menuItem})({}),cn=se("p",{name:"MuiTablePagination",slot:"DisplayedRows",overridesResolver:(t,e)=>e.displayedRows})(({theme:t})=>P({},t.typography.body2,{flexShrink:0}));function un({from:t,to:e,count:n}){return`${t}–${e} of ${n!==-1?n:`more than ${e}`}`}function dn(t){return`Go to ${t} page`}const hn=t=>{const{classes:e}=t;return Ct({root:["root"],toolbar:["toolbar"],spacer:["spacer"],selectLabel:["selectLabel"],select:["select"],input:["input"],selectIcon:["selectIcon"],menuItem:["menuItem"],displayedRows:["displayedRows"],actions:["actions"]},en,e)},fn=S.forwardRef(function(e,n){var a;const s=jt({props:e,name:"MuiTablePagination"}),{ActionsComponent:i=Zt,backIconButtonProps:l,className:c,colSpan:m,component:x=V,count:g,disabled:R=!1,getItemAriaLabel:I=dn,labelDisplayedRows:v=un,labelRowsPerPage:h="Rows per page:",nextIconButtonProps:y,onPageChange:_,onRowsPerPageChange:k,page:q,rowsPerPage:H,rowsPerPageOptions:$=[10,25,50,100],SelectProps:Y={},showFirstButton:j=!1,showLastButton:oe=!1,slotProps:M={},slots:de={}}=s,G=ht(s,tn),K=s,b=hn(K),F=(a=M==null?void 0:M.select)!=null?a:Y,Z=F.native?"option":ln;let ee;(x===V||x==="td")&&(ee=m||1e3);const te=Ve(F.id),ne=Ve(F.labelId),ie=()=>g===-1?(q+1)*H:H===-1?g:Math.min(g,(q+1)*H);return r.jsx(nn,P({colSpan:ee,ref:n,as:x,ownerState:K,className:Re(b.root,c)},G,{children:r.jsxs(an,{className:b.toolbar,children:[r.jsx(rn,{className:b.spacer}),$.length>1&&r.jsx(sn,{className:b.selectLabel,id:ne,children:h}),$.length>1&&r.jsx(on,P({variant:"standard"},!F.variant&&{input:Ke||(Ke=r.jsx(Pt,{}))},{value:H,onChange:k,id:te,labelId:ne},F,{classes:P({},F.classes,{root:Re(b.input,b.selectRoot,(F.classes||{}).root),select:Re(b.select,(F.classes||{}).select),icon:Re(b.selectIcon,(F.classes||{}).icon)}),disabled:R,children:$.map(E=>S.createElement(Z,P({},!Mt(Z)&&{ownerState:K},{className:b.menuItem,key:E.label?E.label:E,value:E.value?E.value:E}),E.label?E.label:E))})),r.jsx(cn,{className:b.displayedRows,children:v({from:g===0?0:q*H+1,to:ie(),count:g===-1?-1:g,page:q})}),r.jsx(i,{className:b.actions,backIconButtonProps:l,count:g,nextIconButtonProps:y,onPageChange:_,page:q,rowsPerPage:H,showFirstButton:j,showLastButton:oe,slotProps:M.actions,slots:de.actions,getItemAriaLabel:I,disabled:R})]})}))});var he={},Ze;function mn(){if(Ze)return he;Ze=1;var t=je();Object.defineProperty(he,"__esModule",{value:!0}),he.default=void 0;var e=t(Pe()),n=Me();return he.default=(0,e.default)((0,n.jsx)("path",{d:"M19 9h-4V3H9v6H5l7 7zM5 18v2h14v-2z"}),"FileDownload"),he}var gn=mn();const xn=Ce(gn);var fe={},et;function bn(){if(et)return fe;et=1;var t=je();Object.defineProperty(fe,"__esModule",{value:!0}),fe.default=void 0;var e=t(Pe()),n=Me();return fe.default=(0,e.default)((0,n.jsx)("path",{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m2 14H7v-2h7zm3-4H7v-2h10zm0-4H7V7h10z"}),"Assignment"),fe}var pn=bn();const wn=Ce(pn);var me={},tt;function yn(){if(tt)return me;tt=1;var t=je();Object.defineProperty(me,"__esModule",{value:!0}),me.default=void 0;var e=t(Pe()),n=Me();return me.default=(0,e.default)((0,n.jsx)("path",{d:"M10 18h4v-2h-4zM3 6v2h18V6zm3 7h12v-2H6z"}),"FilterList"),me}var vn=yn();const jn=Ce(vn);var ge={},nt;function Pn(){if(nt)return ge;nt=1;var t=je();Object.defineProperty(ge,"__esModule",{value:!0}),ge.default=void 0;var e=t(Pe()),n=Me();return ge.default=(0,e.default)((0,n.jsx)("path",{d:"M3 14h4v-4H3zm0 5h4v-4H3zM3 9h4V5H3zm5 5h13v-4H8zm0 5h13v-4H8zM8 5v4h13V5z"}),"ViewList"),ge}var Mn=Pn();const Cn=Ce(Mn);var xe={},at;function Sn(){if(at)return xe;at=1;var t=je();Object.defineProperty(xe,"__esModule",{value:!0}),xe.default=void 0;var e=t(Pe()),n=Me();return xe.default=(0,e.default)((0,n.jsx)("path",{d:"M14.67 5v6.5H9.33V5zm1 6.5H21V5h-5.33zm-1 7.5v-6.5H9.33V19zm1-6.5V19H21v-6.5zm-7.34 0H3V19h5.33zm0-1V5H3v6.5z"}),"ViewModule"),xe}var kn=Sn();const Dn=Ce(kn),ft=6048e5,Rn=864e5,rt=Symbol.for("constructDateFrom");function J(t,e){return typeof t=="function"?t(e):t&&typeof t=="object"&&rt in t?t[rt](e):t instanceof Date?new t.constructor(e):new Date(e)}function Q(t,e){return J(e||t,t)}let In={};function We(){return In}function ve(t,e){var c,m,x,g;const n=We(),a=(e==null?void 0:e.weekStartsOn)??((m=(c=e==null?void 0:e.locale)==null?void 0:c.options)==null?void 0:m.weekStartsOn)??n.weekStartsOn??((g=(x=n.locale)==null?void 0:x.options)==null?void 0:g.weekStartsOn)??0,s=Q(t,e==null?void 0:e.in),i=s.getDay(),l=(i<a?7:0)+i-a;return s.setDate(s.getDate()-l),s.setHours(0,0,0,0),s}function Fe(t,e){return ve(t,{...e,weekStartsOn:1})}function mt(t,e){const n=Q(t,e==null?void 0:e.in),a=n.getFullYear(),s=J(n,0);s.setFullYear(a+1,0,4),s.setHours(0,0,0,0);const i=Fe(s),l=J(n,0);l.setFullYear(a,0,4),l.setHours(0,0,0,0);const c=Fe(l);return n.getTime()>=i.getTime()?a+1:n.getTime()>=c.getTime()?a:a-1}function st(t){const e=Q(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function Bn(t,...e){const n=J.bind(null,e.find(a=>typeof a=="object"));return e.map(n)}function ot(t,e){const n=Q(t,e==null?void 0:e.in);return n.setHours(0,0,0,0),n}function Tn(t,e,n){const[a,s]=Bn(n==null?void 0:n.in,t,e),i=ot(a),l=ot(s),c=+i-st(i),m=+l-st(l);return Math.round((c-m)/Rn)}function On(t,e){const n=mt(t,e),a=J(t,0);return a.setFullYear(n,0,4),a.setHours(0,0,0,0),Fe(a)}function Fn(t){return t instanceof Date||typeof t=="object"&&Object.prototype.toString.call(t)==="[object Date]"}function Wn(t){return!(!Fn(t)&&typeof t!="number"||isNaN(+Q(t)))}function _n(t,e){const n=Q(t,e==null?void 0:e.in);return n.setFullYear(n.getFullYear(),0,1),n.setHours(0,0,0,0),n}const En={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ln=(t,e,n)=>{let a;const s=En[t];return typeof s=="string"?a=s:e===1?a=s.one:a=s.other.replace("{{count}}",e.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+a:a+" ago":a};function Ne(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const qn={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Yn={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},An={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Nn={date:Ne({formats:qn,defaultWidth:"full"}),time:Ne({formats:Yn,defaultWidth:"full"}),dateTime:Ne({formats:An,defaultWidth:"full"})},zn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Hn=(t,e,n,a)=>zn[t];function be(t){return(e,n)=>{const a=n!=null&&n.context?String(n.context):"standalone";let s;if(a==="formatting"&&t.formattingValues){const l=t.defaultFormattingWidth||t.defaultWidth,c=n!=null&&n.width?String(n.width):l;s=t.formattingValues[c]||t.formattingValues[l]}else{const l=t.defaultWidth,c=n!=null&&n.width?String(n.width):t.defaultWidth;s=t.values[c]||t.values[l]}const i=t.argumentCallback?t.argumentCallback(e):e;return s[i]}}const Qn={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Vn={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},$n={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},Gn={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Xn={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Un={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Jn=(t,e)=>{const n=Number(t),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},Kn={ordinalNumber:Jn,era:be({values:Qn,defaultWidth:"wide"}),quarter:be({values:Vn,defaultWidth:"wide",argumentCallback:t=>t-1}),month:be({values:$n,defaultWidth:"wide"}),day:be({values:Gn,defaultWidth:"wide"}),dayPeriod:be({values:Xn,defaultWidth:"wide",formattingValues:Un,defaultFormattingWidth:"wide"})};function pe(t){return(e,n={})=>{const a=n.width,s=a&&t.matchPatterns[a]||t.matchPatterns[t.defaultMatchWidth],i=e.match(s);if(!i)return null;const l=i[0],c=a&&t.parsePatterns[a]||t.parsePatterns[t.defaultParseWidth],m=Array.isArray(c)?ea(c,R=>R.test(l)):Zn(c,R=>R.test(l));let x;x=t.valueCallback?t.valueCallback(m):m,x=n.valueCallback?n.valueCallback(x):x;const g=e.slice(l.length);return{value:x,rest:g}}}function Zn(t,e){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}function ea(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}function ta(t){return(e,n={})=>{const a=e.match(t.matchPattern);if(!a)return null;const s=a[0],i=e.match(t.parsePattern);if(!i)return null;let l=t.valueCallback?t.valueCallback(i[0]):i[0];l=n.valueCallback?n.valueCallback(l):l;const c=e.slice(s.length);return{value:l,rest:c}}}const na=/^(\d+)(th|st|nd|rd)?/i,aa=/\d+/i,ra={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},sa={any:[/^b/i,/^(a|c)/i]},oa={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},ia={any:[/1/i,/2/i,/3/i,/4/i]},la={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},ca={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},ua={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},da={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},ha={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},fa={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},ma={ordinalNumber:ta({matchPattern:na,parsePattern:aa,valueCallback:t=>parseInt(t,10)}),era:pe({matchPatterns:ra,defaultMatchWidth:"wide",parsePatterns:sa,defaultParseWidth:"any"}),quarter:pe({matchPatterns:oa,defaultMatchWidth:"wide",parsePatterns:ia,defaultParseWidth:"any",valueCallback:t=>t+1}),month:pe({matchPatterns:la,defaultMatchWidth:"wide",parsePatterns:ca,defaultParseWidth:"any"}),day:pe({matchPatterns:ua,defaultMatchWidth:"wide",parsePatterns:da,defaultParseWidth:"any"}),dayPeriod:pe({matchPatterns:ha,defaultMatchWidth:"any",parsePatterns:fa,defaultParseWidth:"any"})},ga={code:"en-US",formatDistance:Ln,formatLong:Nn,formatRelative:Hn,localize:Kn,match:ma,options:{weekStartsOn:0,firstWeekContainsDate:1}};function xa(t,e){const n=Q(t,e==null?void 0:e.in);return Tn(n,_n(n))+1}function ba(t,e){const n=Q(t,e==null?void 0:e.in),a=+Fe(n)-+On(n);return Math.round(a/ft)+1}function gt(t,e){var g,R,I,v;const n=Q(t,e==null?void 0:e.in),a=n.getFullYear(),s=We(),i=(e==null?void 0:e.firstWeekContainsDate)??((R=(g=e==null?void 0:e.locale)==null?void 0:g.options)==null?void 0:R.firstWeekContainsDate)??s.firstWeekContainsDate??((v=(I=s.locale)==null?void 0:I.options)==null?void 0:v.firstWeekContainsDate)??1,l=J((e==null?void 0:e.in)||t,0);l.setFullYear(a+1,0,i),l.setHours(0,0,0,0);const c=ve(l,e),m=J((e==null?void 0:e.in)||t,0);m.setFullYear(a,0,i),m.setHours(0,0,0,0);const x=ve(m,e);return+n>=+c?a+1:+n>=+x?a:a-1}function pa(t,e){var c,m,x,g;const n=We(),a=(e==null?void 0:e.firstWeekContainsDate)??((m=(c=e==null?void 0:e.locale)==null?void 0:c.options)==null?void 0:m.firstWeekContainsDate)??n.firstWeekContainsDate??((g=(x=n.locale)==null?void 0:x.options)==null?void 0:g.firstWeekContainsDate)??1,s=gt(t,e),i=J((e==null?void 0:e.in)||t,0);return i.setFullYear(s,0,a),i.setHours(0,0,0,0),ve(i,e)}function wa(t,e){const n=Q(t,e==null?void 0:e.in),a=+ve(n,e)-+pa(n,e);return Math.round(a/ft)+1}function f(t,e){const n=t<0?"-":"",a=Math.abs(t).toString().padStart(e,"0");return n+a}const X={y(t,e){const n=t.getFullYear(),a=n>0?n:1-n;return f(e==="yy"?a%100:a,e.length)},M(t,e){const n=t.getMonth();return e==="M"?String(n+1):f(n+1,2)},d(t,e){return f(t.getDate(),e.length)},a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(t,e){return f(t.getHours()%12||12,e.length)},H(t,e){return f(t.getHours(),e.length)},m(t,e){return f(t.getMinutes(),e.length)},s(t,e){return f(t.getSeconds(),e.length)},S(t,e){const n=e.length,a=t.getMilliseconds(),s=Math.trunc(a*Math.pow(10,n-3));return f(s,e.length)}},ue={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},it={G:function(t,e,n){const a=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});case"GGGG":default:return n.era(a,{width:"wide"})}},y:function(t,e,n){if(e==="yo"){const a=t.getFullYear(),s=a>0?a:1-a;return n.ordinalNumber(s,{unit:"year"})}return X.y(t,e)},Y:function(t,e,n,a){const s=gt(t,a),i=s>0?s:1-s;if(e==="YY"){const l=i%100;return f(l,2)}return e==="Yo"?n.ordinalNumber(i,{unit:"year"}):f(i,e.length)},R:function(t,e){const n=mt(t);return f(n,e.length)},u:function(t,e){const n=t.getFullYear();return f(n,e.length)},Q:function(t,e,n){const a=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(a);case"QQ":return f(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(t,e,n){const a=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(a);case"qq":return f(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(t,e,n){const a=t.getMonth();switch(e){case"M":case"MM":return X.M(t,e);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(t,e,n){const a=t.getMonth();switch(e){case"L":return String(a+1);case"LL":return f(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(t,e,n,a){const s=wa(t,a);return e==="wo"?n.ordinalNumber(s,{unit:"week"}):f(s,e.length)},I:function(t,e,n){const a=ba(t);return e==="Io"?n.ordinalNumber(a,{unit:"week"}):f(a,e.length)},d:function(t,e,n){return e==="do"?n.ordinalNumber(t.getDate(),{unit:"date"}):X.d(t,e)},D:function(t,e,n){const a=xa(t);return e==="Do"?n.ordinalNumber(a,{unit:"dayOfYear"}):f(a,e.length)},E:function(t,e,n){const a=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});case"EEEE":default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(t,e,n,a){const s=t.getDay(),i=(s-a.weekStartsOn+8)%7||7;switch(e){case"e":return String(i);case"ee":return f(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(s,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(s,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(s,{width:"short",context:"formatting"});case"eeee":default:return n.day(s,{width:"wide",context:"formatting"})}},c:function(t,e,n,a){const s=t.getDay(),i=(s-a.weekStartsOn+8)%7||7;switch(e){case"c":return String(i);case"cc":return f(i,e.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(s,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(s,{width:"narrow",context:"standalone"});case"cccccc":return n.day(s,{width:"short",context:"standalone"});case"cccc":default:return n.day(s,{width:"wide",context:"standalone"})}},i:function(t,e,n){const a=t.getDay(),s=a===0?7:a;switch(e){case"i":return String(s);case"ii":return f(s,e.length);case"io":return n.ordinalNumber(s,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});case"iiii":default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(t,e,n){const s=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},b:function(t,e,n){const a=t.getHours();let s;switch(a===12?s=ue.noon:a===0?s=ue.midnight:s=a/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},B:function(t,e,n){const a=t.getHours();let s;switch(a>=17?s=ue.evening:a>=12?s=ue.afternoon:a>=4?s=ue.morning:s=ue.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(s,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(s,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(s,{width:"wide",context:"formatting"})}},h:function(t,e,n){if(e==="ho"){let a=t.getHours()%12;return a===0&&(a=12),n.ordinalNumber(a,{unit:"hour"})}return X.h(t,e)},H:function(t,e,n){return e==="Ho"?n.ordinalNumber(t.getHours(),{unit:"hour"}):X.H(t,e)},K:function(t,e,n){const a=t.getHours()%12;return e==="Ko"?n.ordinalNumber(a,{unit:"hour"}):f(a,e.length)},k:function(t,e,n){let a=t.getHours();return a===0&&(a=24),e==="ko"?n.ordinalNumber(a,{unit:"hour"}):f(a,e.length)},m:function(t,e,n){return e==="mo"?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):X.m(t,e)},s:function(t,e,n){return e==="so"?n.ordinalNumber(t.getSeconds(),{unit:"second"}):X.s(t,e)},S:function(t,e){return X.S(t,e)},X:function(t,e,n){const a=t.getTimezoneOffset();if(a===0)return"Z";switch(e){case"X":return ct(a);case"XXXX":case"XX":return re(a);case"XXXXX":case"XXX":default:return re(a,":")}},x:function(t,e,n){const a=t.getTimezoneOffset();switch(e){case"x":return ct(a);case"xxxx":case"xx":return re(a);case"xxxxx":case"xxx":default:return re(a,":")}},O:function(t,e,n){const a=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+lt(a,":");case"OOOO":default:return"GMT"+re(a,":")}},z:function(t,e,n){const a=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+lt(a,":");case"zzzz":default:return"GMT"+re(a,":")}},t:function(t,e,n){const a=Math.trunc(+t/1e3);return f(a,e.length)},T:function(t,e,n){return f(+t,e.length)}};function lt(t,e=""){const n=t>0?"-":"+",a=Math.abs(t),s=Math.trunc(a/60),i=a%60;return i===0?n+String(s):n+String(s)+e+f(i,2)}function ct(t,e){return t%60===0?(t>0?"-":"+")+f(Math.abs(t)/60,2):re(t,e)}function re(t,e=""){const n=t>0?"-":"+",a=Math.abs(t),s=f(Math.trunc(a/60),2),i=f(a%60,2);return n+s+e+i}const ut=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});case"PPPP":default:return e.date({width:"full"})}},xt=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});case"pppp":default:return e.time({width:"full"})}},ya=(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],a=n[1],s=n[2];if(!s)return ut(t,e);let i;switch(a){case"P":i=e.dateTime({width:"short"});break;case"PP":i=e.dateTime({width:"medium"});break;case"PPP":i=e.dateTime({width:"long"});break;case"PPPP":default:i=e.dateTime({width:"full"});break}return i.replace("{{date}}",ut(a,e)).replace("{{time}}",xt(s,e))},va={p:xt,P:ya},ja=/^D+$/,Pa=/^Y+$/,Ma=["D","DD","YY","YYYY"];function Ca(t){return ja.test(t)}function Sa(t){return Pa.test(t)}function ka(t,e,n){const a=Da(t,e,n);if(console.warn(a),Ma.includes(t))throw new RangeError(a)}function Da(t,e,n){const a=t[0]==="Y"?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${a} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Ra=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Ia=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Ba=/^'([^]*?)'?$/,Ta=/''/g,Oa=/[a-zA-Z]/;function ze(t,e,n){var g,R,I,v;const a=We(),s=a.locale??ga,i=a.firstWeekContainsDate??((R=(g=a.locale)==null?void 0:g.options)==null?void 0:R.firstWeekContainsDate)??1,l=a.weekStartsOn??((v=(I=a.locale)==null?void 0:I.options)==null?void 0:v.weekStartsOn)??0,c=Q(t,n==null?void 0:n.in);if(!Wn(c))throw new RangeError("Invalid time value");let m=e.match(Ia).map(h=>{const y=h[0];if(y==="p"||y==="P"){const _=va[y];return _(h,s.formatLong)}return h}).join("").match(Ra).map(h=>{if(h==="''")return{isToken:!1,value:"'"};const y=h[0];if(y==="'")return{isToken:!1,value:Fa(h)};if(it[y])return{isToken:!0,value:h};if(y.match(Oa))throw new RangeError("Format string contains an unescaped latin alphabet character `"+y+"`");return{isToken:!1,value:h}});s.localize.preprocessor&&(m=s.localize.preprocessor(c,m));const x={firstWeekContainsDate:i,weekStartsOn:l,locale:s};return m.map(h=>{if(!h.isToken)return h.value;const y=h.value;(Sa(y)||Ca(y))&&ka(y,e,String(t));const _=it[y[0]];return _(c,y,s.localize,x)}).join("")}function Fa(t){const e=t.match(Ba);return e?e[1].replace(Ta,"'"):t}function La(){var ke,De;const[t,e]=S.useState([]),[n,a]=S.useState(null),[s,i]=S.useState(!1),[l,c]=S.useState(0),[m,x]=S.useState(6),[g,R]=S.useState(!0),[I,v]=S.useState(null),[h,y]=S.useState(""),[_,k]=S.useState("all"),[q,H]=S.useState("all"),[$,Y]=S.useState("date"),[j,oe]=S.useState("desc"),[M,de]=S.useState("grid"),[G,K]=S.useState(!1),b=kt(),F=S.useCallback(o=>o.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^a-z0-9\s]/g,""),[]),Z=S.useCallback((o,d,u,p,z,L)=>{let w=[...o];if(d){const B=F(d);w=w.filter(T=>F(T.name).includes(B))}return u!=="all"&&(w=w.filter(B=>B.gender===u)),p!=="all"&&(w=w.filter(B=>{const T=B.responses.some(O=>O.completed);return p==="completed"?T:!T})),w.sort((B,T)=>{var He,Qe;let O,W;switch(z){case"name":O=B.name.toLowerCase(),W=T.name.toLowerCase();break;case"gender":O=B.gender,W=T.gender;break;case"responses":O=B.responses.length,W=T.responses.length;break;case"date":default:O=new Date(((He=B.responses[0])==null?void 0:He.date)||0),W=new Date(((Qe=T.responses[0])==null?void 0:Qe.date)||0);break}return O<W?L==="asc"?-1:1:O>W?L==="asc"?1:-1:0}),w},[F]);S.useEffect(()=>{const o=async()=>{try{console.log("QuestionnaireResponses: Iniciando carga de respuestas"),R(!0);const u=await Gt.getAll();if(console.log("QuestionnaireResponses: Resultado de getAll:",u),u.success&&u.data){console.log("QuestionnaireResponses: Datos recibidos correctamente, procesando...");const p=u.data.filter(L=>!L||!L.patient_id||!L.patient?(console.warn("QuestionnaireResponses: Respuesta inválida o sin datos de paciente:",L),!1):!0);console.log("QuestionnaireResponses: Respuestas válidas:",p.length,"de",u.data.length);const z=p.reduce((L,w)=>{console.log("QuestionnaireResponses: Procesando respuesta:",w);const B=w.patient_id,T=L.findIndex(W=>W.id===B),O={id:w.id,patient_id:w.patient_id,date:w.date,answers:w.answers||[],completed:w.completed,answered_questions:w.answered_questions,total_questions:w.total_questions,duration:w.duration||0,patient:w.patient};if(T>=0)console.log("QuestionnaireResponses: Añadiendo respuesta a paciente existente:",L[T].name),L[T].responses.push(O);else{const W=w.patient;W&&W.id&&W.name?(console.log("QuestionnaireResponses: Creando nuevo paciente:",W.name),L.push({id:W.id,name:W.name,gender:W.gender||"O",responses:[O]})):console.warn("QuestionnaireResponses: Datos de paciente inválidos para la respuesta:",w)}return L},[]);console.log("QuestionnaireResponses: Respuestas formateadas:",z),e(z)}else console.error("QuestionnaireResponses: Error en resultado:",u.message),v(u.message||"Error fetching responses")}catch(u){console.error("QuestionnaireResponses: Error loading responses:",u),v(u.message||"Unknown error")}finally{console.log("QuestionnaireResponses: Finalizando carga, estado:",{loading:!1,error:I}),R(!1)}};o();const d=Dt.channel("questionnaire_changes").on("postgres_changes",{event:"*",schema:"public",table:"questionnaire"},async u=>{console.log("Change received!",u);try{console.log("Reloading responses due to real-time update"),await o()}catch(p){console.error("Error reloading responses:",p),v((p==null?void 0:p.message)||"Error updating real-time data")}}).subscribe(u=>{u==="SUBSCRIBED"?(console.log("Successfully subscribed to real-time changes"),v(null)):u==="CLOSED"?console.log("Subscription closed"):u==="CHANNEL_ERROR"&&(console.error("Channel error"),v("Error in real-time connection"))});return()=>{d.unsubscribe()}},[]);const ee=(o,d)=>{a({...d,patient:{id:o.id,name:o.name,gender:o.gender}}),i(!0)},te=()=>{i(!1),a(null)},ne=(o,d)=>{c(d)},ie=o=>{x(parseInt(o.target.value,10)),c(0)},E=()=>{y(""),k("all"),H("all"),Y("date"),oe("desc"),c(0)},le=()=>{let o=0;return h&&o++,_!=="all"&&o++,q!=="all"&&o++,o},_e=(o,d)=>{const u=t.find(z=>z.id===o),p=u==null?void 0:u.responses.find(z=>z.id===d);if(!p||!u){v("No se encontraron los datos del paciente o la respuesta");return}try{if(!p.answers.every((T,O)=>Oe[O]&&Oe[O].text)){v("Error: Algunas preguntas no están disponibles");return}const L=`Paciente,${u.name}
Fecha,${ze(new Date(p.date),"dd/MM/yyyy hh:mm:ss a")}

Pregunta,Respuesta
${p.answers.map((T,O)=>`"${Oe[O].text.replace(/"/g,'""')}",${T===null?"N/A":T?"Verdadero":"Falso"}`).join(`
`)}`,w=new Blob([L],{type:"text/csv;charset=utf-8;"}),B=document.createElement("a");B.href=URL.createObjectURL(w),B.download=`respuestas_${u.name.replace(/[^a-z0-9]/gi,"_")}_${ze(new Date(p.date),"yyyyMMdd")}.csv`,B.click(),URL.revokeObjectURL(B.href)}catch(z){console.error("Error exporting responses:",z),v(z.message||"Error al exportar las respuestas")}},Se=o=>{const d=Math.floor(o/60),u=o%60;return`${d}m ${u}s`};if(g)return console.log("QuestionnaireResponses: Renderizando estado de carga"),r.jsx(C,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:r.jsx(Rt,{})});if(I)return console.log("QuestionnaireResponses: Renderizando estado de error:",I),r.jsx(C,{p:3,children:r.jsx(It,{severity:"error",children:I})});console.log("QuestionnaireResponses: Renderizando contenido principal, respuestas:",t);const ae=Z(t,h,_,q,$,j);return r.jsxs(Bt,{maxWidth:"xl",sx:{py:4},children:[r.jsx(Tt,{in:!0,timeout:800,children:r.jsxs(C,{children:[r.jsx(ce,{elevation:0,sx:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",borderRadius:"24px",p:4,mb:4,color:"white",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,background:'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',opacity:.3}},children:r.jsx(C,{sx:{position:"relative",zIndex:1},children:r.jsxs(C,{sx:{display:"flex",flexDirection:{xs:"column",md:"row"},alignItems:{xs:"center",md:"flex-start"},justifyContent:"space-between",gap:3},children:[r.jsxs(C,{sx:{display:"flex",alignItems:"center",gap:2},children:[r.jsx(C,{sx:{background:"rgba(255,255,255,0.2)",borderRadius:"16px",p:2,backdropFilter:"blur(10px)"},children:r.jsx(wn,{sx:{fontSize:48,color:"white"}})}),r.jsxs(C,{children:[r.jsx(D,{variant:"h3",component:"h1",sx:{fontWeight:"bold",mb:1,fontSize:{xs:"2rem",md:"3rem"}},children:"Respuestas del Cuestionario"}),r.jsx(D,{variant:"h6",sx:{opacity:.9,fontWeight:400},children:"Gestión y análisis de evaluaciones MACI-II"})]})]}),r.jsxs(C,{sx:{display:"flex",alignItems:"center",gap:2,background:"rgba(255,255,255,0.15)",borderRadius:"16px",p:2,backdropFilter:"blur(10px)"},children:[r.jsx(Ot,{sx:{fontSize:32}}),r.jsxs(C,{children:[r.jsxs(D,{variant:"subtitle1",sx:{fontWeight:600},children:[ae.length," Pacientes"]}),r.jsxs(D,{variant:"body2",sx:{opacity:.8},children:[t.reduce((o,d)=>o+d.responses.length,0)," Respuestas totales"]})]})]})]})})}),r.jsxs(ce,{sx:{borderRadius:"20px",p:3,mb:4,background:"linear-gradient(145deg, #f8f9fa, #ffffff)",border:"1px solid #e9ecef",boxShadow:"0 4px 20px rgba(0,0,0,0.08)"},children:[r.jsxs(C,{sx:{mb:3},children:[r.jsxs(C,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2},children:[r.jsxs(D,{variant:"h6",sx:{fontWeight:600,display:"flex",alignItems:"center",gap:1},children:[r.jsx(Ee,{sx:{color:"#1976d2"}}),"Búsqueda y Filtros"]}),r.jsxs(C,{sx:{display:"flex",gap:1},children:[r.jsx(Le,{title:`${le()} filtros activos`,children:r.jsx(Ft,{badgeContent:le(),color:"primary",children:r.jsx(U,{onClick:()=>K(!G),sx:{background:G?"#e3f2fd":"transparent","&:hover":{background:"#e3f2fd"}},children:r.jsx(jn,{})})})}),r.jsx(Le,{title:"Limpiar filtros",children:r.jsx(U,{onClick:E,disabled:le()===0,children:r.jsx(qe,{})})}),r.jsx(Le,{title:`Vista ${M==="grid"?"lista":"cuadrícula"}`,children:r.jsx(U,{onClick:()=>de(M==="grid"?"list":"grid"),children:M==="grid"?r.jsx(Cn,{}):r.jsx(Dn,{})})})]})]}),r.jsx(Wt,{placeholder:"Buscar paciente por nombre...",variant:"outlined",value:h,onChange:o=>y(o.target.value),fullWidth:!0,sx:{"& .MuiOutlinedInput-root":{borderRadius:"12px",backgroundColor:"#f8f9fa","&:hover":{backgroundColor:"#ffffff"},"&.Mui-focused":{backgroundColor:"#ffffff"}}},InputProps:{startAdornment:r.jsx($e,{position:"start",children:r.jsx(Ee,{sx:{color:"#7f8c8d"}})}),endAdornment:h&&r.jsx($e,{position:"end",children:r.jsx(U,{onClick:()=>y(""),size:"small",children:r.jsx(qe,{})})})}})]}),r.jsx(_t,{in:G,children:r.jsxs(C,{sx:{display:G?"block":"none"},children:[r.jsx(Et,{sx:{mb:3}}),r.jsxs(A,{container:!0,spacing:3,children:[r.jsx(A,{item:!0,xs:12,sm:6,md:3,children:r.jsxs(Ie,{fullWidth:!0,children:[r.jsx(Be,{children:"Género"}),r.jsxs(we,{value:_,label:"Género",onChange:o=>k(o.target.value),sx:{borderRadius:"12px"},children:[r.jsx(N,{value:"all",children:"Todos"}),r.jsx(N,{value:"M",children:"Masculino"}),r.jsx(N,{value:"F",children:"Femenino"})]})]})}),r.jsx(A,{item:!0,xs:12,sm:6,md:3,children:r.jsxs(Ie,{fullWidth:!0,children:[r.jsx(Be,{children:"Estado"}),r.jsxs(we,{value:q,label:"Estado",onChange:o=>H(o.target.value),sx:{borderRadius:"12px"},children:[r.jsx(N,{value:"all",children:"Todos"}),r.jsx(N,{value:"completed",children:"Completados"}),r.jsx(N,{value:"incomplete",children:"Incompletos"})]})]})}),r.jsx(A,{item:!0,xs:12,sm:6,md:3,children:r.jsxs(Ie,{fullWidth:!0,children:[r.jsx(Be,{children:"Ordenar por"}),r.jsxs(we,{value:$,label:"Ordenar por",onChange:o=>Y(o.target.value),sx:{borderRadius:"12px"},children:[r.jsx(N,{value:"date",children:"Fecha"}),r.jsx(N,{value:"name",children:"Nombre"}),r.jsx(N,{value:"gender",children:"Género"}),r.jsx(N,{value:"responses",children:"Respuestas"})]})]})}),r.jsx(A,{item:!0,xs:12,sm:6,md:3,children:r.jsxs(Ie,{fullWidth:!0,children:[r.jsx(Be,{children:"Orden"}),r.jsxs(we,{value:j,label:"Orden",onChange:o=>oe(o.target.value),sx:{borderRadius:"12px"},children:[r.jsx(N,{value:"desc",children:"Descendente"}),r.jsx(N,{value:"asc",children:"Ascendente"})]})]})})]})]})})]}),ae.length===0&&(h!==""||_!=="all"||q!=="all")?r.jsxs(ce,{sx:{p:4,mb:3,backgroundColor:"#fff5f5",borderRadius:"16px",border:"1px solid #fed7d7",textAlign:"center"},children:[r.jsx(Ee,{sx:{fontSize:48,color:"#e53e3e",mb:2}}),r.jsx(D,{variant:"h6",color:"#e53e3e",gutterBottom:!0,children:"No se encontraron resultados"}),r.jsx(D,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"No hay pacientes que coincidan con los filtros aplicados"}),r.jsx(Te,{variant:"outlined",onClick:E,startIcon:r.jsx(qe,{}),children:"Limpiar filtros"})]}):ae.length===0?r.jsxs(r.Fragment,{children:[r.jsx(ce,{sx:{p:3,mb:3,backgroundColor:"#f5f5f5"},children:r.jsx(D,{variant:"body1",color:"text.secondary",align:"center",children:"There are no saved responses yet."})}),r.jsx(Lt,{component:ce,sx:{mb:3},children:r.jsxs(qt,{children:[r.jsx(Yt,{children:r.jsxs(Ge,{sx:{backgroundColor:b.palette.grey[50]},children:[r.jsx(V,{children:"Date"}),r.jsx(V,{children:"Status"}),r.jsx(V,{children:"Questions Answered"}),r.jsx(V,{children:"Duration"}),r.jsx(V,{align:"center",children:"Actions"})]})}),r.jsx(At,{children:r.jsx(Ge,{children:r.jsx(V,{colSpan:5,align:"center",children:r.jsx(C,{sx:{py:3},children:r.jsx(D,{variant:"body2",color:"text.secondary",children:"Questionnaire responses will appear here"})})})})})]})})]}):r.jsx(A,{container:!0,spacing:3,children:ae.slice(l*m,l*m+m).map(o=>r.jsx(A,{item:!0,xs:12,sm:6,md:4,children:r.jsxs(Nt,{sx:{display:"flex",flexDirection:"column",justifyContent:"space-between",height:"100%",mb:2,boxShadow:"0 4px 8px rgba(0,0,0,0.1)","&:hover":{boxShadow:"0 8px 16px rgba(0,0,0,0.2)",transform:"translateY(-4px)"},backgroundColor:o.gender==="M"?"#e3f2fd":"#fce4ec",borderRadius:"16px",overflow:"hidden",transition:"transform 0.3s, box-shadow 0.3s"},children:[r.jsxs(zt,{sx:{flexGrow:1},children:[r.jsxs(C,{display:"flex",alignItems:"center",mb:2,children:[o.gender==="M"?r.jsx(Xe,{sx:{mr:1.5,color:"#1976d2",fontSize:"2.5rem"}}):r.jsx(Ue,{sx:{mr:1.5,color:"#d81b60",fontSize:"2.5rem"}}),r.jsx(D,{variant:"h5",sx:{fontWeight:"bold"},children:o.name})]}),o.responses.map(d=>r.jsxs(C,{mb:2,pl:1,children:[r.jsx(Ye,{label:d.completed?"Completado":"En progreso",color:d.completed?"success":"warning",size:"small",sx:{mb:1,fontWeight:"bold"}}),r.jsxs(D,{variant:"body2",color:"text.secondary",children:[d.answered_questions," / ",d.total_questions," preguntas respondidas"]}),r.jsx(Je,{variant:"determinate",value:d.answered_questions/d.total_questions*100,sx:{my:1,height:6,borderRadius:3},color:d.completed?"success":"warning"}),r.jsxs(D,{variant:"caption",color:"text.secondary",children:["Duración: ",Se(d.duration)]})]},d.id))]}),r.jsx(Xt,{sx:{justifyContent:"flex-end",p:2},children:r.jsx(Te,{variant:"contained",size:"small",onClick:()=>ee(o,o.responses[0]),sx:{borderRadius:"12px"},children:"Ver Detalles"})})]})},o.id))}),r.jsx(C,{sx:{display:"flex",justifyContent:"center",mt:4},children:r.jsx(fn,{component:"div",count:ae.length,page:l,onPageChange:ne,rowsPerPage:m,onRowsPerPageChange:ie,rowsPerPageOptions:[6,12,24],labelRowsPerPage:"Pacientes por página",sx:{"& .MuiTablePagination-toolbar":{backgroundColor:"#f8f9fa",borderRadius:"12px",px:2}}})})]})}),r.jsxs(Ht,{open:s,onClose:te,maxWidth:"lg",fullWidth:!0,disableEnforceFocus:!0,disableAutoFocus:!0,PaperProps:{sx:{maxHeight:"90vh"}},children:[r.jsxs(Qt,{sx:{backgroundColor:b.palette.primary.main,color:b.palette.primary.contrastText,display:"flex",alignItems:"center",justifyContent:"space-between",padding:"16px 24px"},children:[r.jsxs(C,{display:"flex",alignItems:"center",children:[((ke=n==null?void 0:n.patient)==null?void 0:ke.gender)==="M"?r.jsx(Xe,{sx:{mr:1,fontSize:"1.5rem"}}):r.jsx(Ue,{sx:{mr:1,fontSize:"1.5rem"}}),r.jsxs(D,{variant:"h6",component:"div",children:["Responses of ",(De=n==null?void 0:n.patient)==null?void 0:De.name]})]}),r.jsx(Ye,{label:n!=null&&n.completed?"Completed":"In progress",color:n!=null&&n.completed?"success":"warning",size:"small",sx:{fontWeight:500}})]}),r.jsxs(Vt,{dividers:!0,children:[r.jsx(C,{mb:2,p:2,bgcolor:"#f5f5f5",borderRadius:1,children:r.jsxs(A,{container:!0,spacing:2,children:[r.jsx(A,{item:!0,xs:12,sm:6,children:r.jsxs(D,{variant:"body2",color:"text.secondary",children:["Date: ",r.jsx("strong",{children:n&&ze(new Date(n.date),"dd/MM/yyyy hh:mm:ss a")})]})}),r.jsx(A,{item:!0,xs:12,sm:6,children:r.jsxs(D,{variant:"body2",color:"text.secondary",children:["Duration: ",r.jsx("strong",{children:n&&Se(n.duration)})]})}),r.jsx(A,{item:!0,xs:12,children:r.jsxs(D,{variant:"body2",color:"text.secondary",children:["Questions answered: ",r.jsxs("strong",{children:[n==null?void 0:n.answered_questions," of ",n==null?void 0:n.total_questions]}),r.jsx(Je,{variant:"determinate",value:((n==null?void 0:n.answered_questions)||0)/((n==null?void 0:n.total_questions)||1)*100,sx:{mt:1,height:8,borderRadius:4},color:n!=null&&n.completed?"success":"primary"})]})})]})}),r.jsx(D,{variant:"h6",gutterBottom:!0,sx:{mt:2,mb:3,borderBottom:`1px solid ${b.palette.divider}`,pb:1},children:"Responses Detail"}),r.jsx(A,{container:!0,spacing:2,children:n==null?void 0:n.answers.map((o,d)=>{var u;return r.jsx(A,{item:!0,xs:12,sm:6,md:4,children:r.jsxs(ce,{elevation:3,sx:{p:2,borderLeft:`4px solid ${o===null?b.palette.grey[400]:o?b.palette.success.main:b.palette.error.main}`,backgroundColor:o===null?Ae(b.palette.grey[200],.5):o?Ae(b.palette.success.light,.1):Ae(b.palette.error.light,.1),height:"100%",display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[r.jsxs(D,{variant:"body2",sx:{mb:2,fontWeight:500},children:[d+1,". ",((u=Oe[d])==null?void 0:u.text)||`Question ${d+1}`]}),r.jsx(Ye,{label:o===null?"No answer":o?"True":"False",color:o===null?"default":o?"success":"error",size:"small",sx:{alignSelf:"flex-end"}})]})},`${n.id}-${d}`)})})]}),r.jsxs($t,{children:[r.jsx(Te,{onClick:te,color:"primary",children:"Close"}),r.jsx(Te,{onClick:()=>{var o;return n&&_e(((o=n.patient)==null?void 0:o.id)||"",n.id)},color:"primary",variant:"contained",startIcon:r.jsx(xn,{}),children:"Export CSV"})]})]})]})}export{La as default};
