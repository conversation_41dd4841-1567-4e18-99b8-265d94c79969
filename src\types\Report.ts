// Tipos para los reportes MACI-II

export interface TestScores {
  personalityPatterns?: { [key: string]: number };
  expressedConcerns?: { [key: string]: number };
  clinicalSyndromes?: { [key: string]: number };
  grossmanFacets?: { [key: string]: number };
  validityScales?: { [key: string]: number };
}

export interface ConvertedScores {
  personalityPatterns?: { [key: string]: { pc: number | string } };
  expressedConcerns?: { [key: string]: { pc: number | string } };
  clinicalSyndromes?: { [key: string]: { pc: number | string } };
  grossmanFacets?: { [key: string]: { pc: number | string } };
  validityScales?: { [key: string]: { pc: number | string } };
}

export interface Report {
  id: string;
  patient_id: string;
  response_id: string;
  title: string;
  content: string;
  qualitative_interpretation: string;
  scores: TestScores; // Puntajes PD (raw scores)
  converted_scores: ConvertedScores; // Puntajes PC (percentile scores)
  created_at: string;
  updated_at: string;
  patient?: {
    id?: string;
    name?: string;
    first_name?: string;
    last_name?: string;
    birth_date?: string;
    gender?: string;
  };
  // Propiedades adicionales para compatibilidad
  patient_name?: string;
  patient_birth_date?: string;
  test_date?: string;
  // Propiedades con estructura alternativa para acceso directo
  personality_patterns?: { [key: string]: { raw_score: number | null; pc_score: number | null } };
  expressed_concerns?: { [key: string]: { raw_score: number | null; pc_score: number | null } };
  clinical_syndromes?: { [key: string]: { raw_score: number | null; pc_score: number | null } };
  grossman_facets?: { [key: string]: { raw_score: number | null; pc_score: number | null } };
  validity_scales?: { [key: string]: { raw_score: number | null; pc_score: number | null } };
}