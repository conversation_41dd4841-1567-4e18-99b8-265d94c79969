import{t as s}from"./index-ClZegq-Q.js";class c{async ensureTableExists(){try{const{error:e}=await s.from("reports").select("id").limit(1);return e&&e.message.includes('relation "reports" does not exist')?(console.warn("La tabla reports no existe. Por favor, ejecute el script SQL para crearla."),!1):!0}catch(e){return console.warn("Error verificando tabla reports:",e),!1}}async createReport(e){if(!await this.ensureTableExists())throw new Error("La tabla reports no existe. Por favor, ejecute el script SQL para crearla.");const{data:t,error:o}=await s.from("reports").insert({patient_id:e.patient_id,response_id:e.response_id,title:e.title,content:e.content,qualitative_interpretation:e.qualitative_interpretation,scores:e.scores,converted_scores:e.converted_scores}).select().single();if(o)throw console.error("Error creating report:",o),new Error(`Error al crear el informe: ${o.message}`);return t}async getReports(){await this.ensureTableExists();const{data:e,error:r}=await s.from("reports").select("*").order("created_at",{ascending:!1});if(r)throw console.error("Error fetching reports:",r),new Error(`Error al obtener los informes: ${r.message}`);return await Promise.all((e||[]).map(async o=>{var a,i;if(o.patient_id)try{const{data:n}=await s.from("patients").select("id, name, birth_date, gender").eq("id",o.patient_id).single();return{...o,patient:n?{id:n.id,name:n.name,first_name:((a=n.name)==null?void 0:a.split(" ")[0])||"",last_name:((i=n.name)==null?void 0:i.split(" ").slice(1).join(" "))||"",birth_date:n.birth_date,gender:n.gender}:null}}catch(n){return console.warn("Error fetching patient data:",n),o}return o}))}async getReportById(e){const{data:r,error:t}=await s.from("reports").select(`
        *,
        patient:patients(
          first_name,
          last_name,
          birth_date,
          gender
        ),
        scores,
        converted_scores
      `).eq("id",e).single();if(t){if(t.code==="PGRST116")return null;throw console.error("Error fetching report:",t),new Error(`Error al obtener el informe: ${t.message}`)}return r}async getReportsByPatient(e){const{data:r,error:t}=await s.from("reports").select(`
        *,
        patient:patients(
          first_name,
          last_name,
          birth_date,
          gender
        ),
        scores,
        converted_scores
      `).eq("patient_id",e).order("created_at",{ascending:!1});if(t)throw console.error("Error fetching patient reports:",t),new Error(`Error al obtener los informes del paciente: ${t.message}`);return r||[]}async deleteReport(e){const{error:r}=await s.from("reports").delete().eq("id",e);if(r)throw console.error("Error deleting report:",r),new Error(`Error al eliminar el informe: ${r.message}`)}async updateReport(e,r){const{data:t,error:o}=await s.from("reports").update({...r,updated_at:new Date().toISOString(),scores:r.scores,converted_scores:r.converted_scores}).eq("id",e).select(`
        *,
        patient:patients(
          first_name,
          last_name,
          birth_date,
          gender
        ),
        scores,
        converted_scores
      `).single();if(o)throw console.error("Error updating report:",o),new Error(`Error al actualizar el informe: ${o.message}`);return t}async generateReport(e,r){throw new Error("Method not implemented")}async getPatientHistory(e){throw new Error("Method not implemented")}async exportReport(e,r){throw new Error("Method not implemented")}}const p=new c;export{p as r};
