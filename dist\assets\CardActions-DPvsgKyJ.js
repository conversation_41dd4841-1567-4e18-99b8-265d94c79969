import{b as d,g as p,r as u,d as g,_ as C,a as e,j as m,s as f,f as x,k as A}from"./index-ClZegq-Q.js";function b(s){return d("MuiCardActions",s)}p("MuiCardActions",["root","spacing"]);const y=["disableSpacing","className"],S=s=>{const{classes:t,disableSpacing:o}=s;return A({root:["root",!o&&"spacing"]},b,t)},R=f("div",{name:"MuiCardActions",slot:"Root",overridesResolver:(s,t)=>{const{ownerState:o}=s;return[t.root,!o.disableSpacing&&t.spacing]}})(({ownerState:s})=>e({display:"flex",alignItems:"center",padding:8},!s.disableSpacing&&{"& > :not(style) ~ :not(style)":{marginLeft:8}})),M=u.forwardRef(function(t,o){const a=g({props:t,name:"MuiCardActions"}),{disableSpacing:i=!1,className:r}=a,c=C(a,y),n=e({},a,{disableSpacing:i}),l=S(n);return m.jsx(R,e({className:x(l.root,r),ownerState:n,ref:o},c))});export{M as C};
