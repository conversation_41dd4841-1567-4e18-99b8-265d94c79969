import{m as s,n as u,o as l,p as c,ap as x,j as r,v as d,B as m,x as n,U as h}from"./index-ClZegq-Q.js";var e={},o;function p(){if(o)return e;o=1;var t=s();Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=t(u()),i=l();return e.default=(0,a.default)((0,i.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8"}),"ErrorOutline"),e}var v=p();const f=c(v);function j(){const t=x();return r.jsx(d,{maxWidth:"lg",sx:{py:4},children:r.jsxs(m,{sx:{minHeight:"80vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center",gap:3},children:[r.jsx(f,{sx:{fontSize:100,color:"error.main"}}),r.jsx(n,{variant:"h2",component:"h1",color:"error",children:"404"}),r.jsx(n,{variant:"h4",component:"h2",color:"text.primary",children:"Página no encontrada"}),r.jsx(n,{variant:"body1",color:"text.secondary",sx:{maxWidth:600},children:"Lo sentimos, la página que estás buscando no existe o ha sido movida."}),r.jsx(h,{variant:"contained",color:"primary",size:"large",onClick:()=>t("/"),sx:{mt:2,borderRadius:2,textTransform:"none",px:4},children:"Volver al inicio"})]})})}export{j as default};
