import React from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import { Report } from '../../types/Report';

interface NewGraphicsSectionProps {
  report: Report;
}

// Función para obtener el color de la barra según el puntaje PC
const getBarColor = (pcScore: number): string => {
  if (pcScore >= 85) return '#dc3545'; // Rojo para puntajes altos
  if (pcScore >= 75) return '#fd7e14'; // Naranja para puntajes moderadamente altos
  if (pcScore >= 60) return '#ffc107'; // Amarillo para puntajes moderados
  return '#007bff'; // Azul para puntajes bajos
};

// Función para obtener el ancho de la barra como porcentaje
const getBarWidth = (pcScore: number): number => {
  return Math.min(pcScore, 100);
};

// Función auxiliar para obtener puntajes con compatibilidad hacia atrás
const getScoreData = (report: Report, category: string, scaleId: string) => {
  // Intentar obtener de la estructura nueva primero
  const newStructure = (report as any)[category]?.[scaleId];
  if (newStructure) {
    return {
      pdScore: newStructure.raw_score,
      pcScore: newStructure.pc_score
    };
  }
  
  // Fallback a la estructura antigua
  const pdScore = report.scores?.[category as keyof typeof report.scores]?.[scaleId];
  const pcScore = report.converted_scores?.[category as keyof typeof report.converted_scores]?.[scaleId]?.pc;
  
  return {
    pdScore: typeof pdScore === 'number' ? pdScore : null,
    pcScore: typeof pcScore === 'number' ? pcScore : (typeof pcScore === 'string' ? parseInt(pcScore) : null)
  };
};

// Componente para una fila de escala con barra
const ScaleRow: React.FC<{
  scaleId: string;
  scaleName: string;
  pdScore: number | null;
  pcScore: number | null;
  isSubcategory?: boolean;
}> = ({ scaleId, scaleName, pdScore, pcScore, isSubcategory = false }) => {
  const barColor = getBarColor(pcScore || 0);
  const barWidth = getBarWidth(pcScore || 0);

  return (
    <TableRow>
      <TableCell 
        sx={{ 
          textAlign: 'center',
          width: '80px',
          padding: '12px 8px'
        }}
      >
        <Box
          className="scale-circle"
          sx={{
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            backgroundColor: '#048799',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            fontSize: '14px',
            margin: '0 auto'
          }}
        >
          {scaleId}
        </Box>
      </TableCell>
      <TableCell 
        className="scale-name"
        sx={{ 
          color: '#000000',
          fontWeight: 'bold',
          fontSize: '14px',
          width: '300px',
          paddingLeft: '16px'
        }}
      >
        {scaleName}
      </TableCell>
      <TableCell 
        sx={{ 
          textAlign: 'center',
          fontWeight: 'bold',
          width: '60px',
          fontSize: '14px'
        }}
      >
        {pdScore || 0}
      </TableCell>
      <TableCell 
        sx={{ 
          textAlign: 'center',
          fontWeight: 'bold',
          width: '60px',
          fontSize: '14px'
        }}
      >
        {pcScore || 0}
      </TableCell>
      <TableCell sx={{ width: '400px', padding: '8px' }}>
        <Box className="progress-bar" sx={{ position: 'relative', height: '20px', backgroundColor: '#f0f0f0' }}>
          <Box
            className="progress-fill"
            sx={{
              position: 'absolute',
              left: 0,
              top: 0,
              height: '100%',
              width: `${barWidth}%`,
              backgroundColor: barColor,
              transition: 'width 0.3s ease'
            }}
          />
        </Box>
      </TableCell>
    </TableRow>
  );
};

// Componente para una sección de escalas
const ScaleSection: React.FC<{
  title: string;
  scales: Array<{
    id: string;
    name: string;
    pdScore: number | null;
    pcScore: number | null;
  }>;
}> = ({ title, scales }) => {
  return (
    <Box sx={{ mb: 4 }}>
      <TableContainer component={Paper} elevation={2}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell 
                colSpan={5}
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '16px',
                  padding: '12px'
                }}
              >
                {title}
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                S
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                {title.toUpperCase()}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                PD
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                PC
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                PERFIL DE LAS TASAS BASE
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {scales.map((scale) => (
              <ScaleRow
                key={scale.id}
                scaleId={scale.id}
                scaleName={scale.name}
                pdScore={scale.pdScore}
                pcScore={scale.pcScore}
                isSubcategory={false}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

// Componente para las Facetas de Grossman con subcategorías
const GrossmanFacetsSection: React.FC<{
  report: Report;
}> = ({ report }) => {
  // Función auxiliar para obtener datos de Grossman
  const getGrossmanData = (scaleId: string) => {
    return getScoreData(report, 'grossmanFacets', scaleId);
  };

  // Crear todas las escalas de Grossman en formato plano para usar ScaleSection
  const grossmanScales = [
    // Faceta 1 - Introversivo
    { id: '1', name: 'Introversivo', subtitle: 'Tendencia a la introversión y aislamiento social', ...getGrossmanData('1') },
    { id: '1.1', name: 'Expresivamente Impasible', ...getGrossmanData('1.1'), isSubcategory: true },
    { id: '1.2', name: 'Temperamentalmente Apático', ...getGrossmanData('1.2'), isSubcategory: true },
    { id: '1.3', name: 'Interpersonalmente Desvinculado', ...getGrossmanData('1.3'), isSubcategory: true },
    
    // Faceta 2 - Inhibido
    { id: '2', name: 'Inhibido', subtitle: 'Patrones de timidez e inseguridad social', ...getGrossmanData('2') },
    { id: '2.1', name: 'Expresivamente Inquieto', ...getGrossmanData('2.1'), isSubcategory: true },
    { id: '2.2', name: 'Interpersonalmente Aversivo', ...getGrossmanData('2.2'), isSubcategory: true },
    { id: '2.3', name: 'Autoimagen Alienada', ...getGrossmanData('2.3'), isSubcategory: true },
    
    // Faceta 3 - Sumiso
    { id: '3', name: 'Sumiso', subtitle: 'Patrones de dependencia y sumisión hacia otros', ...getGrossmanData('3') },
    { id: '3.1', name: 'Interpersonalmente Dócil', ...getGrossmanData('3.1'), isSubcategory: true },
    { id: '3.2', name: 'Temperamentalmente Pacifista', ...getGrossmanData('3.2'), isSubcategory: true },
    { id: '3.3', name: 'Expresivamente Incompetente', ...getGrossmanData('3.3'), isSubcategory: true },
    
    // Faceta 4 - Dramatizante
    { id: '4', name: 'Dramatizante', subtitle: 'Patrón histriónico y teatral de comportamiento', ...getGrossmanData('4') },
    { id: '4.1', name: 'Interpersonalmente Buscador de Atención', ...getGrossmanData('4.1'), isSubcategory: true },
    { id: '4.2', name: 'Autoimagen Gregaria', ...getGrossmanData('4.2'), isSubcategory: true },
    { id: '4.3', name: 'Temperamentalmente Voluble', ...getGrossmanData('4.3'), isSubcategory: true },
    
    // Faceta 5 - Egoísta
    { id: '5', name: 'Egoísta', subtitle: 'Patrón narcisista con sentido exagerado de valor propio', ...getGrossmanData('5') },
    { id: '5.1', name: 'Autoimagen Admirable', ...getGrossmanData('5.1'), isSubcategory: true },
    { id: '5.2', name: 'Cognitivamente Expansivo', ...getGrossmanData('5.2'), isSubcategory: true },
    { id: '5.3', name: 'Interpersonalmente Explotador', ...getGrossmanData('5.3'), isSubcategory: true },
    
    // Faceta 6A - Indisciplinado
    { id: '6A', name: 'Indisciplinado', subtitle: 'Comportamiento impulsivo y desafiante', ...getGrossmanData('6A') },
    { id: '6A.1', name: 'Expresivamente Impulsivo', ...getGrossmanData('6A.1'), isSubcategory: true },
    { id: '6A.2', name: 'Mecanismo de Actuar', ...getGrossmanData('6A.2'), isSubcategory: true },
    { id: '6A.3', name: 'Interpersonalmente Irresponsable', ...getGrossmanData('6A.3'), isSubcategory: true },
    
    // Faceta 6B - Imponente
    { id: '6B', name: 'Imponente', subtitle: 'Comportamiento hostil, abusivo o dominante', ...getGrossmanData('6B') },
    { id: '6B.1', name: 'Interpersonalmente Abrasivo', ...getGrossmanData('6B.1'), isSubcategory: true },
    { id: '6B.2', name: 'Expresivamente Precipitado', ...getGrossmanData('6B.2'), isSubcategory: true },
    { id: '6B.3', name: 'Temperamentalmente Hostil', ...getGrossmanData('6B.3'), isSubcategory: true },
    
    // Faceta 7 - Conformista
    { id: '7', name: 'Conformista', subtitle: 'Adherencia extrema a normas y convenciones', ...getGrossmanData('7') },
    { id: '7.1', name: 'Expresivamente Disciplinado', ...getGrossmanData('7.1'), isSubcategory: true },
    { id: '7.2', name: 'Interpersonalmente Respetuoso', ...getGrossmanData('7.2'), isSubcategory: true },
    { id: '7.3', name: 'Autoimagen Concienzuda', ...getGrossmanData('7.3'), isSubcategory: true },
    
    // Faceta 8A - Descontento
    { id: '8A', name: 'Descontento', subtitle: 'Insatisfacción crónica y resentimiento', ...getGrossmanData('8A') },
    { id: '8A.1', name: 'Autoimagen Disconforme', ...getGrossmanData('8A.1'), isSubcategory: true },
    { id: '8A.2', name: 'Expresivamente Resentido', ...getGrossmanData('8A.2'), isSubcategory: true },
    { id: '8A.3', name: 'Interpersonalmente Contrario', ...getGrossmanData('8A.3'), isSubcategory: true },
    
    // Faceta 8B - Agraviado
    { id: '8B', name: 'Agraviado', subtitle: 'Sensación de ser víctima y sentimientos de injusticia', ...getGrossmanData('8B') },
    { id: '8B.1', name: 'Cognitivamente Indeciso', ...getGrossmanData('8B.1'), isSubcategory: true },
    { id: '8B.2', name: 'Autoimagen No Merecedor', ...getGrossmanData('8B.2'), isSubcategory: true },
    { id: '8B.3', name: 'Temperamentalmente Disfórico', ...getGrossmanData('8B.3'), isSubcategory: true },
    
    // Faceta 9 - Tendencia Límite
    { id: '9', name: 'Tendencia Límite', subtitle: 'Inestabilidad emocional y conflictos interpersonales', ...getGrossmanData('9') },
    { id: '9.1', name: 'Temperamentalmente Lábil', ...getGrossmanData('9.1'), isSubcategory: true },
    { id: '9.2', name: 'Interpersonalmente Paradójico', ...getGrossmanData('9.2'), isSubcategory: true },
    { id: '9.3', name: 'Autoimagen Incierta', ...getGrossmanData('9.3'), isSubcategory: true }
  ];

  // Crear un componente especial para Grossman que use ScaleSection
  const GrossmanScaleRow: React.FC<{
    scaleId: string;
    scaleName: string;
    pdScore: number | null;
    pcScore: number | null;
    isSubcategory?: boolean;
    subtitle?: string;
  }> = ({ scaleId, scaleName, pdScore, pcScore, isSubcategory = false, subtitle }) => {
    const barColor = getBarColor(pcScore || 0);
    const barWidth = getBarWidth(pcScore || 0);

    return (
      <TableRow>
        <TableCell 
          sx={{ 
            textAlign: 'center',
            width: '80px',
            padding: '12px 8px'
          }}
        >
          {isSubcategory ? (
            // Para subcategorías: solo texto sin círculo
            <Typography
              sx={{
                color: '#048799',
                fontWeight: 'bold',
                fontSize: '12px'
              }}
            >
              {scaleId}
            </Typography>
          ) : (
            // Para escalas principales: círculo
            <Box
              sx={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                backgroundColor: '#048799',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 'bold',
                fontSize: '14px',
                margin: '0 auto'
              }}
            >
              {scaleId}
            </Box>
          )}
        </TableCell>
        <TableCell 
          sx={{ 
            color: '#000000',
            fontWeight: isSubcategory ? 'normal' : 'bold',
            fontSize: isSubcategory ? '13px' : '14px',
            width: '300px',
            paddingLeft: isSubcategory ? '24px' : '16px'
          }}
        >
          {!isSubcategory && subtitle ? (
            <Box>
              <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#000000', fontSize: '14px' }}>
                {scaleName}
              </Typography>
              <Typography variant="caption" sx={{ color: '#666', fontStyle: 'italic' }}>
                {subtitle}
              </Typography>
            </Box>
          ) : scaleName}
        </TableCell>
        <TableCell 
          sx={{ 
            textAlign: 'center',
            fontWeight: 'bold',
            width: '60px',
            fontSize: '14px'
          }}
        >
          {pdScore || 0}
        </TableCell>
        <TableCell 
          sx={{ 
            textAlign: 'center',
            fontWeight: 'bold',
            width: '60px',
            fontSize: '14px'
          }}
        >
          {pcScore || 0}
        </TableCell>
        <TableCell sx={{ width: '400px', padding: '8px' }}>
          <Box sx={{ position: 'relative', height: isSubcategory ? '16px' : '20px', backgroundColor: '#f0f0f0' }}>
            <Box
              sx={{
                position: 'absolute',
                left: 0,
                top: 0,
                height: '100%',
                width: `${barWidth}%`,
                backgroundColor: barColor,
                transition: 'width 0.3s ease'
              }}
            />
          </Box>
        </TableCell>
      </TableRow>
    );
  };

  return (
    <Box sx={{ mb: 4 }}>
      <TableContainer component={Paper} elevation={2}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell 
                colSpan={5}
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '16px',
                  padding: '12px'
                }}
              >
                FACETAS DE GROSSMAN
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px',
                  width: '80px'
                }}
              >
                S
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                FACETAS DE GROSSMAN
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                PD
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                PC
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: '#1e3a5f',
                  color: 'white',
                  textAlign: 'center',
                  fontWeight: 'bold',
                  fontSize: '14px'
                }}
              >
                PERFIL DE LAS TASAS BASE
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {grossmanScales.map((scale) => {
              const isMainFacet = !scale.isSubcategory;
              const displayName = isMainFacet && scale.subtitle 
                ? (
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#000000', fontSize: '14px' }}>
                        {scale.name}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#666', fontStyle: 'italic' }}>
                        {scale.subtitle}
                      </Typography>
                    </Box>
                  )
                : scale.name;

              return (
                <TableRow key={scale.id}>
                  <TableCell 
                    sx={{ 
                      textAlign: 'center',
                      width: '80px',
                      padding: '12px 8px'
                    }}
                  >
                    {scale.isSubcategory ? (
                      // Para subcategorías: solo texto sin círculo
                      <Typography
                        sx={{
                          color: '#048799',
                          fontWeight: 'bold',
                          fontSize: '13px'
                        }}
                      >
                        {scale.id}
                      </Typography>
                    ) : (
                      // Para escalas principales: círculo
                      <Box
                        sx={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '50%',
                          backgroundColor: '#048799',
                          color: 'white',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontWeight: 'bold',
                          fontSize: '14px',
                          margin: '0 auto'
                        }}
                      >
                        {scale.id}
                      </Box>
                    )}
                  </TableCell>
                  <TableCell 
                    sx={{ 
                      color: '#000000',
                      fontWeight: scale.isSubcategory ? 'normal' : 'bold',
                      fontSize: scale.isSubcategory ? '13px' : '14px',
                      width: '300px',
                      paddingLeft: scale.isSubcategory ? '24px' : '16px'
                    }}
                  >
                    {displayName}
                  </TableCell>
                  <TableCell 
                    sx={{ 
                      textAlign: 'center',
                      fontWeight: 'bold',
                      width: '60px',
                      fontSize: '14px'
                    }}
                  >
                    {scale.pdScore || 0}
                  </TableCell>
                  <TableCell 
                    sx={{ 
                      textAlign: 'center',
                      fontWeight: 'bold',
                      width: '60px',
                      fontSize: '14px'
                    }}
                  >
                    {scale.pcScore || 0}
                  </TableCell>
                  <TableCell sx={{ width: '400px', padding: '8px' }}>
                    <Box sx={{ position: 'relative', height: scale.isSubcategory ? '16px' : '20px', backgroundColor: '#f0f0f0' }}>
                      <Box
                        sx={{
                          position: 'absolute',
                          left: 0,
                          top: 0,
                          height: '100%',
                          width: `${getBarWidth(scale.pcScore || 0)}%`,
                          backgroundColor: getBarColor(scale.pcScore || 0),
                          transition: 'width 0.3s ease'
                        }}
                      />
                    </Box>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

// Componente principal
const NewGraphicsSectionComplete: React.FC<NewGraphicsSectionProps> = ({ report }) => {
  // Función para crear datos de escalas con compatibilidad
  const createScaleData = (category: string, scales: Array<{id: string, name: string}>) => {
    return scales.map(scale => {
      const { pdScore, pcScore } = getScoreData(report, category, scale.id);
      return {
        id: scale.id,
        name: scale.name,
        pdScore,
        pcScore
      };
    });
  };

  const personalityPatterns = createScaleData('personalityPatterns', [
    { id: '1', name: 'Introvertido' },
    { id: '2', name: 'Inhibido' },
    { id: '3', name: 'Sumiso' },
    { id: '4', name: 'Dramático' },
    { id: '5', name: 'Egocéntrico' },
    { id: '6A', name: 'Rebelde' },
    { id: '6B', name: 'Hostil' },
    { id: '7', name: 'Conformista' },
    { id: '8A', name: 'Resentido' },
    { id: '8B', name: 'Agraviado' },
    { id: '9', name: 'Tendencia límite' }
  ]);

  const expressedConcerns = createScaleData('expressedConcerns', [
    { id: 'A', name: 'Difusión de identidad' },
    { id: 'B', name: 'Infravaloración de uno mismo' },
    { id: 'C', name: 'Inseguridad con otros adolescentes' },
    { id: 'D', name: 'Desavenencias familiares' }
  ]);

  const clinicalSyndromes = createScaleData('clinicalSyndromes', [
    { id: 'AA', name: 'Patrones de ingesta por atracón' },
    { id: 'BB', name: 'Propensión al abuso de sustancias' },
    { id: 'CC', name: 'Predisposición a la delincuencia' },
    { id: 'DD', name: 'Estados de ansiedad' },
    { id: 'EE', name: 'Ánimo depresivo' },
    { id: 'FF', name: 'Tendencia suicida' },
    { id: 'GG', name: 'Desregulación del estado de ánimo' },
    { id: 'HH', name: 'Estrés postraumático' },
    { id: 'II', name: 'Distorsiones de la realidad' }
  ]);

  return (
    <Box sx={{ mb: 4 }}>
      <Typography 
        variant="h4" 
        sx={{ 
          color: '#1e3a5f', 
          fontWeight: 'bold', 
          mb: 4,
          textAlign: 'center',
          fontSize: '1.8rem',
          textTransform: 'uppercase'
        }}
      >
        2. ANÁLISIS GRÁFICO POR ESCALAS
      </Typography>

      {/* Patrones de Personalidad */}
      <ScaleSection
        title="PATRONES DE PERSONALIDAD"
        scales={personalityPatterns}
      />

      {/* Preocupaciones Expresadas */}
      <ScaleSection
        title="PREOCUPACIONES EXPRESADAS"
        scales={expressedConcerns}
      />

      {/* Síndromes Clínicos */}
      <ScaleSection
        title="SÍNDROMES CLÍNICOS"
        scales={clinicalSyndromes}
      />

      {/* Facetas de Grossman */}
      <GrossmanFacetsSection report={report} />
    </Box>
  );
};

export default React.memo(NewGraphicsSectionComplete);
export { NewGraphicsSectionComplete };